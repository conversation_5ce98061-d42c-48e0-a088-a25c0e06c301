/**
 * Chrome Extension DOM Optimizer
 * Optimized for content script isolation and cross-frame communication
 */

class ExtensionDOMOptimizer {
    constructor() {
        this.batchQueue = [];
        this.batchTimeout = null;
        this.batchDelay = 16; // ~60fps
        this.maxBatchSize = 50;
        this.isContentScript = typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.getManifest;
        this.shadowRoots = new WeakMap();
        this.elementCache = new Map();
        this.cacheMaxSize = 1000;
        this.observerConfig = {
            childList: true,
            subtree: true,
            attributes: true,
            attributeOldValue: true
        };
        this.mutationObserver = null;
        this.frameOptimizations = new Map();
        
        this.init();
    }
    
    init() {
        this.setupMutationObserver();
        this.setupMemoryPressureHandling();
        this.setupFrameOptimizations();
        this.bindToExtensionLifecycle();
    }
    
    setupMutationObserver() {
        if (typeof MutationObserver !== 'undefined') {
            this.mutationObserver = new MutationObserver((mutations) => {
                this.handleMutations(mutations);
            });
            
            // Start observing if document is ready
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', () => {
                    this.startObserving();
                });
            } else {
                this.startObserving();
            }
        }
    }
    
    startObserving() {
        if (this.mutationObserver && document.body) {
            this.mutationObserver.observe(document.body, this.observerConfig);
        }
    }
    
    setupMemoryPressureHandling() {
        if (typeof chrome !== 'undefined' && chrome.runtime) {
            chrome.runtime.onMessage.addListener((message) => {
                if (message.type === 'MEMORY_PRESSURE') {
                    this.handleMemoryPressure(message.level);
                }
            });
        }
    }
    
    setupFrameOptimizations() {
        // Detect if we're in an iframe
        if (window !== window.top) {
            this.setupCrossFrameOptimizations();
        }
    }
    
    setupCrossFrameOptimizations() {
        // Reduce DOM query frequency in iframes
        this.batchDelay = 32; // Reduce to ~30fps for iframes
        this.maxBatchSize = 25; // Smaller batches for iframes
        
        // Listen for frame visibility changes
        if (typeof IntersectionObserver !== 'undefined') {
            const frameObserver = new IntersectionObserver((entries) => {
                const isVisible = entries[0].isIntersecting;
                this.adjustPerformanceForVisibility(isVisible);
            });
            
            if (document.documentElement) {
                frameObserver.observe(document.documentElement);
            }
        }
    }
    
    adjustPerformanceForVisibility(isVisible) {
        if (isVisible) {
            this.batchDelay = 16; // Normal performance
            this.maxBatchSize = 50;
        } else {
            this.batchDelay = 100; // Reduced performance for hidden frames
            this.maxBatchSize = 10;
        }
    }
    
    bindToExtensionLifecycle() {
        // Handle content script destruction
        if (typeof window !== 'undefined') {
            window.addEventListener('beforeunload', () => {
                this.cleanup();
            });
        }
        
        // Handle page visibility changes
        if (typeof document !== 'undefined') {
            document.addEventListener('visibilitychange', () => {
                if (document.hidden) {
                    this.pauseOptimizations();
                } else {
                    this.resumeOptimizations();
                }
            });
        }
    }
    
    batchDOMOperation(operation) {
        return new Promise((resolve, reject) => {
            const batchItem = {
                operation,
                resolve,
                reject,
                timestamp: Date.now()
            };
            
            this.batchQueue.push(batchItem);
            
            // Process immediately if batch is full
            if (this.batchQueue.length >= this.maxBatchSize) {
                this.processBatch();
            } else if (!this.batchTimeout) {
                // Schedule batch processing
                this.batchTimeout = setTimeout(() => {
                    this.processBatch();
                }, this.batchDelay);
            }
        });
    }
    
    processBatch() {
        if (this.batchQueue.length === 0) return;
        
        const batch = this.batchQueue.splice(0, this.maxBatchSize);
        this.batchTimeout = null;
        
        // Use requestAnimationFrame for better performance
        const processFrame = () => {
            const startTime = performance.now();
            const maxFrameTime = 8; // Max 8ms per frame to maintain 60fps
            
            while (batch.length > 0 && (performance.now() - startTime) < maxFrameTime) {
                const item = batch.shift();
                
                try {
                    const result = item.operation();
                    item.resolve(result);
                } catch (error) {
                    item.reject(error);
                }
            }
            
            // Continue processing remaining items in next frame
            if (batch.length > 0) {
                requestAnimationFrame(processFrame);
            }
        };
        
        if (typeof requestAnimationFrame !== 'undefined') {
            requestAnimationFrame(processFrame);
        } else {
            // Fallback for environments without requestAnimationFrame
            setTimeout(processFrame, 0);
        }
        
        // Schedule next batch if queue has more items
        if (this.batchQueue.length > 0 && !this.batchTimeout) {
            this.batchTimeout = setTimeout(() => {
                this.processBatch();
            }, this.batchDelay);
        }
    }
    
    async createElement(tagName, attributes = {}, textContent = '') {
        return this.batchDOMOperation(() => {
            const element = document.createElement(tagName);
            
            // Set attributes efficiently
            for (const [key, value] of Object.entries(attributes)) {
                if (key === 'className') {
                    element.className = value;
                } else if (key === 'style' && typeof value === 'object') {
                    Object.assign(element.style, value);
                } else {
                    element.setAttribute(key, value);
                }
            }
            
            if (textContent) {
                element.textContent = textContent;
            }
            
            return element;
        });
    }
    
    async appendChild(parent, child) {
        return this.batchDOMOperation(() => {
            if (typeof parent === 'string') {
                parent = this.getCachedElement(parent);
            }
            
            if (parent && child) {
                parent.appendChild(child);
                return child;
            }
            
            throw new Error('Invalid parent or child element');
        });
    }
    
    async removeElement(element) {
        return this.batchDOMOperation(() => {
            if (typeof element === 'string') {
                element = this.getCachedElement(element);
            }
            
            if (element && element.parentNode) {
                element.parentNode.removeChild(element);
                this.clearElementFromCache(element);
                return true;
            }
            
            return false;
        });
    }
    
    async updateElement(element, updates) {
        return this.batchDOMOperation(() => {
            if (typeof element === 'string') {
                element = this.getCachedElement(element);
            }
            
            if (!element) {
                throw new Error('Element not found');
            }
            
            // Batch multiple updates
            if (updates.attributes) {
                for (const [key, value] of Object.entries(updates.attributes)) {
                    element.setAttribute(key, value);
                }
            }
            
            if (updates.style) {
                Object.assign(element.style, updates.style);
            }
            
            if (updates.className !== undefined) {
                element.className = updates.className;
            }
            
            if (updates.textContent !== undefined) {
                element.textContent = updates.textContent;
            }
            
            if (updates.innerHTML !== undefined) {
                // Use innerHTML carefully in extension context
                if (this.isContentScript) {
                    // Sanitize HTML in content scripts
                    element.innerHTML = this.sanitizeHTML(updates.innerHTML);
                } else {
                    element.innerHTML = updates.innerHTML;
                }
            }
            
            return element;
        });
    }
    
    sanitizeHTML(html) {
        // Basic HTML sanitization for content scripts
        const div = document.createElement('div');
        div.textContent = html;
        return div.innerHTML;
    }
    
    getCachedElement(selector) {
        if (this.elementCache.has(selector)) {
            const cached = this.elementCache.get(selector);
            
            // Verify element is still in DOM
            if (document.contains(cached.element)) {
                return cached.element;
            } else {
                this.elementCache.delete(selector);
            }
        }
        
        const element = document.querySelector(selector);
        if (element) {
            // Manage cache size
            if (this.elementCache.size >= this.cacheMaxSize) {
                const firstKey = this.elementCache.keys().next().value;
                this.elementCache.delete(firstKey);
            }
            
            this.elementCache.set(selector, {
                element,
                timestamp: Date.now()
            });
        }
        
        return element;
    }
    
    clearElementFromCache(element) {
        for (const [selector, cached] of this.elementCache) {
            if (cached.element === element) {
                this.elementCache.delete(selector);
                break;
            }
        }
    }
    
    async createShadowDOM(hostElement, options = {}) {
        return this.batchDOMOperation(() => {
            if (typeof hostElement === 'string') {
                hostElement = this.getCachedElement(hostElement);
            }
            
            if (!hostElement) {
                throw new Error('Host element not found');
            }
            
            const shadowRoot = hostElement.attachShadow({
                mode: options.mode || 'closed',
                ...options
            });
            
            this.shadowRoots.set(hostElement, shadowRoot);
            return shadowRoot;
        });
    }
    
    getShadowRoot(hostElement) {
        if (typeof hostElement === 'string') {
            hostElement = this.getCachedElement(hostElement);
        }
        
        return this.shadowRoots.get(hostElement);
    }
    
    handleMutations(mutations) {
        // Throttle mutation handling to avoid performance issues
        const now = Date.now();
        if (this.lastMutationHandle && (now - this.lastMutationHandle) < 100) {
            return;
        }
        this.lastMutationHandle = now;
        
        // Clear cache for removed elements
        for (const mutation of mutations) {
            if (mutation.type === 'childList') {
                for (const removedNode of mutation.removedNodes) {
                    if (removedNode.nodeType === Node.ELEMENT_NODE) {
                        this.clearElementFromCache(removedNode);
                    }
                }
            }
        }
    }
    
    handleMemoryPressure(level) {
        console.warn(`DOM optimizer handling memory pressure: ${level}`);
        
        switch (level) {
            case 'emergency':
                this.elementCache.clear();
                this.batchQueue.length = 0;
                this.maxBatchSize = 10;
                break;
            case 'critical':
                this.clearOldCacheEntries();
                this.maxBatchSize = 25;
                break;
            case 'warning':
                this.clearOldCacheEntries(60000); // Clear entries older than 1 minute
                break;
        }
    }
    
    clearOldCacheEntries(maxAge = 300000) { // Default 5 minutes
        const cutoff = Date.now() - maxAge;
        
        for (const [selector, cached] of this.elementCache) {
            if (cached.timestamp < cutoff) {
                this.elementCache.delete(selector);
            }
        }
    }
    
    pauseOptimizations() {
        if (this.batchTimeout) {
            clearTimeout(this.batchTimeout);
            this.batchTimeout = null;
        }
        
        if (this.mutationObserver) {
            this.mutationObserver.disconnect();
        }
    }
    
    resumeOptimizations() {
        this.startObserving();
        
        if (this.batchQueue.length > 0) {
            this.processBatch();
        }
    }
    
    getStats() {
        return {
            batchQueueSize: this.batchQueue.length,
            elementCacheSize: this.elementCache.size,
            shadowRootsCount: this.shadowRoots.size || 0,
            batchDelay: this.batchDelay,
            maxBatchSize: this.maxBatchSize,
            isContentScript: this.isContentScript
        };
    }
    
    cleanup() {
        // Clear all pending operations
        this.batchQueue.length = 0;
        
        if (this.batchTimeout) {
            clearTimeout(this.batchTimeout);
        }
        
        if (this.mutationObserver) {
            this.mutationObserver.disconnect();
        }
        
        // Clear caches
        this.elementCache.clear();
        this.shadowRoots = new WeakMap();
        
        console.log('ExtensionDOMOptimizer cleaned up');
    }
}

// Create global instance
window.ExtensionDOMOptimizer = window.ExtensionDOMOptimizer || new ExtensionDOMOptimizer();

// Export for modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ExtensionDOMOptimizer;
}
