/**
 * Chrome Extension Memory Monitor
 * Tracks extension-specific memory usage and implements cleanup strategies
 */

class ExtensionMemoryMonitor {
    constructor() {
        this.thresholds = {
            warning: 50 * 1024 * 1024,    // 50MB
            critical: 100 * 1024 * 1024,  // 100MB
            emergency: 150 * 1024 * 1024  // 150MB
        };
        
        this.metrics = {
            extensionMemory: 0,
            serviceWorkerMemory: 0,
            contentScriptMemory: 0,
            storageUsage: 0,
            cacheSize: 0,
            lastCheck: Date.now(),
            peakUsage: 0
        };
        
        this.cleanupStrategies = [];
        this.monitoringInterval = null;
        this.isContentScript = typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.getManifest;
        this.memoryAPI = null;
        this.performanceObserver = null;
        
        this.init();
    }
    
    init() {
        this.detectMemoryAPIs();
        this.setupPerformanceObserver();
        this.startMonitoring();
        this.registerDefaultCleanupStrategies();
        this.setupStorageMonitoring();
    }
    
    detectMemoryAPIs() {
        // Check for available memory APIs
        if (typeof performance !== 'undefined' && performance.measureUserAgentSpecificMemory) {
            this.memoryAPI = 'measureUserAgentSpecificMemory';
        } else if (typeof chrome !== 'undefined' && chrome.system && chrome.system.memory) {
            this.memoryAPI = 'chrome.system.memory';
        } else if (typeof performance !== 'undefined' && performance.memory) {
            this.memoryAPI = 'performance.memory';
        }
        
        console.log(`Using memory API: ${this.memoryAPI || 'none'}`);
    }
    
    setupPerformanceObserver() {
        if (typeof PerformanceObserver !== 'undefined') {
            try {
                this.performanceObserver = new PerformanceObserver((list) => {
                    this.handlePerformanceEntries(list.getEntries());
                });
                
                this.performanceObserver.observe({ 
                    entryTypes: ['measure', 'navigation', 'resource'] 
                });
            } catch (error) {
                console.warn('PerformanceObserver not available:', error);
            }
        }
    }
    
    handlePerformanceEntries(entries) {
        for (const entry of entries) {
            if (entry.entryType === 'measure' && entry.name.includes('memory')) {
                this.metrics.lastMeasurement = {
                    name: entry.name,
                    duration: entry.duration,
                    timestamp: entry.startTime
                };
            }
        }
    }
    
    startMonitoring() {
        // Monitor every 30 seconds
        this.monitoringInterval = setInterval(() => {
            this.checkMemoryUsage();
        }, 30000);
        
        // Initial check
        this.checkMemoryUsage();
    }
    
    async checkMemoryUsage() {
        try {
            const memoryInfo = await this.getMemoryInfo();
            this.updateMetrics(memoryInfo);
            
            const totalUsage = this.calculateTotalUsage();
            this.metrics.peakUsage = Math.max(this.metrics.peakUsage, totalUsage);
            
            // Check thresholds
            if (totalUsage > this.thresholds.emergency) {
                await this.handleMemoryPressure('emergency');
            } else if (totalUsage > this.thresholds.critical) {
                await this.handleMemoryPressure('critical');
            } else if (totalUsage > this.thresholds.warning) {
                await this.handleMemoryPressure('warning');
            }
            
            this.metrics.lastCheck = Date.now();
        } catch (error) {
            console.error('Memory monitoring failed:', error);
        }
    }
    
    async getMemoryInfo() {
        const info = {
            total: 0,
            used: 0,
            available: 0,
            extension: 0
        };
        
        try {
            switch (this.memoryAPI) {
                case 'measureUserAgentSpecificMemory':
                    const measurement = await performance.measureUserAgentSpecificMemory();
                    info.total = measurement.bytes;
                    info.extension = this.extractExtensionMemory(measurement);
                    break;
                    
                case 'chrome.system.memory':
                    const systemInfo = await chrome.system.memory.getInfo();
                    info.total = systemInfo.capacity;
                    info.available = systemInfo.availableCapacity;
                    info.used = info.total - info.available;
                    break;
                    
                case 'performance.memory':
                    info.total = performance.memory.totalJSHeapSize;
                    info.used = performance.memory.usedJSHeapSize;
                    info.available = performance.memory.jsHeapSizeLimit - info.used;
                    break;
                    
                default:
                    // Estimate based on DOM and cache sizes
                    info.extension = this.estimateExtensionMemory();
            }
        } catch (error) {
            console.error('Failed to get memory info:', error);
            info.extension = this.estimateExtensionMemory();
        }
        
        return info;
    }
    
    extractExtensionMemory(measurement) {
        // Extract extension-specific memory from detailed measurement
        let extensionMemory = 0;
        
        if (measurement.breakdown) {
            for (const breakdown of measurement.breakdown) {
                if (breakdown.attribution && breakdown.attribution.includes('extension')) {
                    extensionMemory += breakdown.bytes;
                }
            }
        }
        
        return extensionMemory || measurement.bytes * 0.1; // Estimate 10% if no breakdown
    }
    
    estimateExtensionMemory() {
        let estimate = 0;
        
        // Estimate DOM memory
        if (typeof document !== 'undefined') {
            const elements = document.querySelectorAll('*').length;
            estimate += elements * 100; // ~100 bytes per element
        }
        
        // Estimate cache memory
        if (window.ExtensionTemplateCache) {
            const stats = window.ExtensionTemplateCache.getCacheStats();
            estimate += stats.memorySize || 0;
        }
        
        // Estimate timer memory
        if (window.ExtensionTimerManager) {
            const stats = window.ExtensionTimerManager.getTimerStats();
            estimate += stats.activeTimers * 1000; // ~1KB per timer
        }
        
        return estimate;
    }
    
    updateMetrics(memoryInfo) {
        this.metrics.extensionMemory = memoryInfo.extension || 0;
        this.metrics.totalSystemMemory = memoryInfo.total || 0;
        this.metrics.availableMemory = memoryInfo.available || 0;
        this.metrics.usedMemory = memoryInfo.used || 0;
    }
    
    calculateTotalUsage() {
        return this.metrics.extensionMemory + this.metrics.storageUsage + this.metrics.cacheSize;
    }
    
    async handleMemoryPressure(level) {
        console.warn(`Memory pressure detected: ${level} (${this.formatBytes(this.calculateTotalUsage())})`);
        
        // Notify background script
        if (this.isContentScript) {
            try {
                chrome.runtime.sendMessage({
                    type: 'MEMORY_PRESSURE',
                    level: level,
                    metrics: this.metrics
                });
            } catch (error) {
                console.error('Failed to notify background of memory pressure:', error);
            }
        }
        
        // Execute cleanup strategies
        for (const strategy of this.cleanupStrategies) {
            if (strategy.level === level || strategy.level === 'all') {
                try {
                    await strategy.cleanup();
                    console.log(`Executed cleanup strategy: ${strategy.name}`);
                } catch (error) {
                    console.error(`Cleanup strategy failed: ${strategy.name}`, error);
                }
            }
        }
        
        // Force garbage collection if available
        if (level === 'emergency' && typeof window !== 'undefined' && window.gc) {
            window.gc();
        }
    }
    
    registerDefaultCleanupStrategies() {
        // Template cache cleanup
        this.registerCleanupStrategy({
            name: 'template-cache-cleanup',
            level: 'warning',
            cleanup: async () => {
                if (window.ExtensionTemplateCache) {
                    await window.ExtensionTemplateCache.clearCache();
                }
            }
        });
        
        // DOM optimizer cleanup
        this.registerCleanupStrategy({
            name: 'dom-optimizer-cleanup',
            level: 'critical',
            cleanup: async () => {
                if (window.ExtensionDOMOptimizer) {
                    window.ExtensionDOMOptimizer.handleMemoryPressure('critical');
                }
            }
        });
        
        // Timer cleanup
        this.registerCleanupStrategy({
            name: 'timer-cleanup',
            level: 'emergency',
            cleanup: async () => {
                if (window.ExtensionTimerManager) {
                    window.ExtensionTimerManager.handleMemoryPressure('emergency');
                }
            }
        });
        
        // Storage cleanup
        this.registerCleanupStrategy({
            name: 'storage-cleanup',
            level: 'critical',
            cleanup: async () => {
                await this.cleanupOldStorageEntries();
            }
        });
    }
    
    registerCleanupStrategy(strategy) {
        this.cleanupStrategies.push(strategy);
    }
    
    async cleanupOldStorageEntries() {
        try {
            const result = await chrome.storage.local.get(null);
            const cutoffTime = Date.now() - (24 * 60 * 60 * 1000); // 24 hours
            const keysToRemove = [];
            
            for (const [key, value] of Object.entries(result)) {
                if (value && value.timestamp && value.timestamp < cutoffTime) {
                    keysToRemove.push(key);
                }
            }
            
            if (keysToRemove.length > 0) {
                await chrome.storage.local.remove(keysToRemove);
                console.log(`Cleaned up ${keysToRemove.length} old storage entries`);
            }
        } catch (error) {
            console.error('Failed to cleanup old storage entries:', error);
        }
    }
    
    setupStorageMonitoring() {
        // Monitor storage usage
        if (typeof chrome !== 'undefined' && chrome.storage) {
            setInterval(async () => {
                try {
                    const usage = await chrome.storage.local.getBytesInUse();
                    this.metrics.storageUsage = usage;
                } catch (error) {
                    console.error('Failed to get storage usage:', error);
                }
            }, 60000); // Check every minute
        }
    }
    
    formatBytes(bytes) {
        if (bytes === 0) return '0 Bytes';
        
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    
    getMemoryReport() {
        const totalUsage = this.calculateTotalUsage();
        
        return {
            summary: {
                totalUsage: this.formatBytes(totalUsage),
                peakUsage: this.formatBytes(this.metrics.peakUsage),
                thresholdStatus: this.getThresholdStatus(totalUsage),
                lastCheck: new Date(this.metrics.lastCheck).toISOString()
            },
            breakdown: {
                extensionMemory: this.formatBytes(this.metrics.extensionMemory),
                storageUsage: this.formatBytes(this.metrics.storageUsage),
                cacheSize: this.formatBytes(this.metrics.cacheSize)
            },
            system: {
                totalSystemMemory: this.formatBytes(this.metrics.totalSystemMemory),
                availableMemory: this.formatBytes(this.metrics.availableMemory),
                usedMemory: this.formatBytes(this.metrics.usedMemory)
            },
            thresholds: {
                warning: this.formatBytes(this.thresholds.warning),
                critical: this.formatBytes(this.thresholds.critical),
                emergency: this.formatBytes(this.thresholds.emergency)
            },
            cleanupStrategies: this.cleanupStrategies.map(s => ({
                name: s.name,
                level: s.level
            }))
        };
    }
    
    getThresholdStatus(usage) {
        if (usage > this.thresholds.emergency) return 'emergency';
        if (usage > this.thresholds.critical) return 'critical';
        if (usage > this.thresholds.warning) return 'warning';
        return 'normal';
    }
    
    setThresholds(newThresholds) {
        this.thresholds = { ...this.thresholds, ...newThresholds };
    }
    
    cleanup() {
        if (this.monitoringInterval) {
            clearInterval(this.monitoringInterval);
        }
        
        if (this.performanceObserver) {
            this.performanceObserver.disconnect();
        }
        
        console.log('ExtensionMemoryMonitor cleaned up');
    }
}

// Create global instance
window.ExtensionMemoryMonitor = window.ExtensionMemoryMonitor || new ExtensionMemoryMonitor();

// Export for modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ExtensionMemoryMonitor;
}
