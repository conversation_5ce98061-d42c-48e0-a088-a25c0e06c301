/**
 * Chrome Extension Timer Manager
 * Handles service worker lifecycle and content script suspension
 */

class ExtensionTimerManager {
    constructor() {
        this.timers = new Map();
        this.persistentTimers = new Map();
        this.backgroundPort = null;
        this.isContentScript = typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.getManifest;
        this.suspendedTimers = new Map();
        this.timerCategories = {
            CRITICAL: 'critical',
            BACKGROUND: 'background',
            UI: 'ui'
        };
        
        this.init();
    }
    
    init() {
        this.setupBackgroundConnection();
        this.setupLifecycleHandlers();
        this.restoreSuspendedTimers();
    }
    
    setupBackgroundConnection() {
        if (this.isContentScript) {
            try {
                this.backgroundPort = chrome.runtime.connect({ name: 'timer-manager' });
                
                this.backgroundPort.onMessage.addListener((message) => {
                    this.handleBackgroundMessage(message);
                });
                
                this.backgroundPort.onDisconnect.addListener(() => {
                    console.warn('Background connection lost, attempting reconnection');
                    setTimeout(() => this.setupBackgroundConnection(), 1000);
                });
            } catch (error) {
                console.error('Failed to connect to background:', error);
            }
        }
    }
    
    setupLifecycleHandlers() {
        // Handle page visibility changes
        if (typeof document !== 'undefined') {
            document.addEventListener('visibilitychange', () => {
                if (document.hidden) {
                    this.handleSuspension();
                } else {
                    this.handleResumption();
                }
            });
        }
        
        // Handle beforeunload
        if (typeof window !== 'undefined') {
            window.addEventListener('beforeunload', () => {
                this.handleSuspension();
            });
        }
        
        // Handle extension messages
        if (typeof chrome !== 'undefined' && chrome.runtime) {
            chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
                if (message.type === 'MEMORY_PRESSURE') {
                    this.handleMemoryPressure(message.level);
                }
            });
        }
    }
    
    handleBackgroundMessage(message) {
        switch (message.type) {
            case 'TIMER_CALLBACK':
                this.executeTimerCallback(message.timerId);
                break;
            case 'MEMORY_PRESSURE':
                this.handleMemoryPressure(message.level);
                break;
        }
    }
    
    setTimeout(callback, delay, category = this.timerCategories.UI, options = {}) {
        const timerId = this.generateTimerId();
        
        const timerInfo = {
            id: timerId,
            callback,
            delay,
            category,
            options,
            type: 'timeout',
            created: Date.now(),
            persistent: options.persistent || false
        };
        
        if (timerInfo.persistent && this.isContentScript) {
            return this.createPersistentTimer(timerInfo);
        }
        
        const nativeTimerId = setTimeout(() => {
            this.executeTimer(timerId);
        }, delay);
        
        timerInfo.nativeId = nativeTimerId;
        this.timers.set(timerId, timerInfo);
        
        return timerId;
    }
    
    setInterval(callback, interval, category = this.timerCategories.UI, options = {}) {
        const timerId = this.generateTimerId();
        
        const timerInfo = {
            id: timerId,
            callback,
            interval,
            category,
            options,
            type: 'interval',
            created: Date.now(),
            persistent: options.persistent || false
        };
        
        if (timerInfo.persistent && this.isContentScript) {
            return this.createPersistentTimer(timerInfo);
        }
        
        const nativeTimerId = setInterval(() => {
            this.executeTimer(timerId);
        }, interval);
        
        timerInfo.nativeId = nativeTimerId;
        this.timers.set(timerId, timerInfo);
        
        return timerId;
    }
    
    async createPersistentTimer(timerInfo) {
        try {
            const intervalMinutes = timerInfo.interval ? timerInfo.interval / (1000 * 60) : timerInfo.delay / (1000 * 60);
            
            const response = await this.sendToBackground({
                type: 'CREATE_PERSISTENT_TIMER',
                id: timerInfo.id,
                callback: timerInfo.callback.toString(),
                intervalMinutes: Math.max(intervalMinutes, 0.1), // Minimum 6 seconds
                options: {
                    ...timerInfo.options,
                    category: timerInfo.category,
                    critical: timerInfo.category === this.timerCategories.CRITICAL
                }
            });
            
            if (response.success) {
                this.persistentTimers.set(timerInfo.id, timerInfo);
                return timerInfo.id;
            } else {
                console.error('Failed to create persistent timer:', response.error);
                // Fallback to regular timer
                return this.setTimeout(timerInfo.callback, timerInfo.delay || timerInfo.interval, timerInfo.category, { ...timerInfo.options, persistent: false });
            }
        } catch (error) {
            console.error('Failed to create persistent timer:', error);
            // Fallback to regular timer
            return this.setTimeout(timerInfo.callback, timerInfo.delay || timerInfo.interval, timerInfo.category, { ...timerInfo.options, persistent: false });
        }
    }
    
    clearTimeout(timerId) {
        if (this.persistentTimers.has(timerId)) {
            this.clearPersistentTimer(timerId);
            return;
        }
        
        const timerInfo = this.timers.get(timerId);
        if (timerInfo && timerInfo.nativeId) {
            clearTimeout(timerInfo.nativeId);
            this.timers.delete(timerId);
        }
    }
    
    clearInterval(timerId) {
        if (this.persistentTimers.has(timerId)) {
            this.clearPersistentTimer(timerId);
            return;
        }
        
        const timerInfo = this.timers.get(timerId);
        if (timerInfo && timerInfo.nativeId) {
            clearInterval(timerInfo.nativeId);
            this.timers.delete(timerId);
        }
    }
    
    async clearPersistentTimer(timerId) {
        try {
            await this.sendToBackground({
                type: 'CLEAR_PERSISTENT_TIMER',
                id: timerId
            });
            
            this.persistentTimers.delete(timerId);
        } catch (error) {
            console.error('Failed to clear persistent timer:', error);
        }
    }
    
    executeTimer(timerId) {
        const timerInfo = this.timers.get(timerId);
        if (timerInfo) {
            try {
                timerInfo.callback();
                
                // Remove timeout timers after execution
                if (timerInfo.type === 'timeout') {
                    this.timers.delete(timerId);
                }
            } catch (error) {
                console.error(`Timer callback failed for ${timerId}:`, error);
            }
        }
    }
    
    executeTimerCallback(timerId) {
        // Handle callbacks from background script
        const timerInfo = this.persistentTimers.get(timerId);
        if (timerInfo) {
            try {
                // Reconstruct function if it's a string
                let callback = timerInfo.callback;
                if (typeof callback === 'string') {
                    callback = new Function('return ' + callback)();
                }
                callback();
            } catch (error) {
                console.error(`Persistent timer callback failed for ${timerId}:`, error);
            }
        }
    }
    
    handleSuspension() {
        console.log('Handling timer suspension');
        
        // Store current timers for restoration
        const suspendedData = {
            timers: Array.from(this.timers.entries()).map(([id, info]) => ({
                id,
                callback: info.callback.toString(),
                remaining: this.calculateRemainingTime(info),
                category: info.category,
                options: info.options,
                type: info.type
            })),
            timestamp: Date.now()
        };
        
        // Store in session storage for quick restoration
        if (typeof sessionStorage !== 'undefined') {
            sessionStorage.setItem('suspendedTimers', JSON.stringify(suspendedData));
        }
        
        // Clear all non-persistent timers
        for (const [timerId, timerInfo] of this.timers) {
            if (!timerInfo.persistent && timerInfo.nativeId) {
                if (timerInfo.type === 'timeout') {
                    clearTimeout(timerInfo.nativeId);
                } else {
                    clearInterval(timerInfo.nativeId);
                }
            }
        }
        
        this.timers.clear();
    }
    
    handleResumption() {
        console.log('Handling timer resumption');
        this.restoreSuspendedTimers();
    }
    
    restoreSuspendedTimers() {
        try {
            if (typeof sessionStorage === 'undefined') return;
            
            const suspendedData = sessionStorage.getItem('suspendedTimers');
            if (!suspendedData) return;
            
            const data = JSON.parse(suspendedData);
            const suspensionDuration = Date.now() - data.timestamp;
            
            for (const timerData of data.timers) {
                try {
                    // Reconstruct callback function
                    const callback = new Function('return ' + timerData.callback)();
                    
                    if (timerData.type === 'timeout') {
                        const adjustedDelay = Math.max(0, timerData.remaining - suspensionDuration);
                        this.setTimeout(callback, adjustedDelay, timerData.category, timerData.options);
                    } else if (timerData.type === 'interval') {
                        this.setInterval(callback, timerData.remaining, timerData.category, timerData.options);
                    }
                } catch (error) {
                    console.error('Failed to restore timer:', error);
                }
            }
            
            // Clear suspended timer data
            sessionStorage.removeItem('suspendedTimers');
        } catch (error) {
            console.error('Failed to restore suspended timers:', error);
        }
    }
    
    calculateRemainingTime(timerInfo) {
        if (timerInfo.type === 'interval') {
            return timerInfo.interval;
        }
        
        // For timeouts, calculate remaining time
        const elapsed = Date.now() - timerInfo.created;
        return Math.max(0, timerInfo.delay - elapsed);
    }
    
    handleMemoryPressure(level) {
        console.warn(`Timer manager handling memory pressure: ${level}`);
        
        switch (level) {
            case 'emergency':
                this.clearNonCriticalTimers();
                break;
            case 'critical':
                this.reduceTimerFrequency(0.5);
                break;
            case 'warning':
                this.clearUITimers();
                break;
        }
    }
    
    clearNonCriticalTimers() {
        for (const [timerId, timerInfo] of this.timers) {
            if (timerInfo.category !== this.timerCategories.CRITICAL) {
                this.clearTimeout(timerId);
            }
        }
    }
    
    clearUITimers() {
        for (const [timerId, timerInfo] of this.timers) {
            if (timerInfo.category === this.timerCategories.UI) {
                this.clearTimeout(timerId);
            }
        }
    }
    
    reduceTimerFrequency(factor) {
        // Increase intervals for non-critical timers
        for (const [timerId, timerInfo] of this.timers) {
            if (timerInfo.category !== this.timerCategories.CRITICAL && timerInfo.type === 'interval') {
                const newInterval = timerInfo.interval * (1 / factor);
                
                // Clear old timer
                clearInterval(timerInfo.nativeId);
                
                // Create new timer with reduced frequency
                const nativeTimerId = setInterval(() => {
                    this.executeTimer(timerId);
                }, newInterval);
                
                timerInfo.nativeId = nativeTimerId;
                timerInfo.interval = newInterval;
            }
        }
    }
    
    async sendToBackground(message) {
        return new Promise((resolve, reject) => {
            if (!this.backgroundPort) {
                reject(new Error('No background connection'));
                return;
            }
            
            const messageId = this.generateTimerId();
            message.messageId = messageId;
            
            const timeout = setTimeout(() => {
                reject(new Error('Background message timeout'));
            }, 5000);
            
            const listener = (response) => {
                if (response.messageId === messageId) {
                    clearTimeout(timeout);
                    this.backgroundPort.onMessage.removeListener(listener);
                    resolve(response);
                }
            };
            
            this.backgroundPort.onMessage.addListener(listener);
            this.backgroundPort.postMessage(message);
        });
    }
    
    generateTimerId() {
        return `timer_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    
    getTimerStats() {
        return {
            activeTimers: this.timers.size,
            persistentTimers: this.persistentTimers.size,
            categories: {
                critical: Array.from(this.timers.values()).filter(t => t.category === this.timerCategories.CRITICAL).length,
                background: Array.from(this.timers.values()).filter(t => t.category === this.timerCategories.BACKGROUND).length,
                ui: Array.from(this.timers.values()).filter(t => t.category === this.timerCategories.UI).length
            }
        };
    }
    
    cleanup() {
        // Clear all timers
        for (const [timerId] of this.timers) {
            this.clearTimeout(timerId);
        }
        
        // Disconnect from background
        if (this.backgroundPort) {
            this.backgroundPort.disconnect();
        }
    }
}

// Create global instance
window.ExtensionTimerManager = window.ExtensionTimerManager || new ExtensionTimerManager();

// Export for modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ExtensionTimerManager;
}
