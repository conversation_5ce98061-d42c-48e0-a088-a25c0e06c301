/**
 * Chrome Extension Template Cache Manager
 * Optimized for extension storage limits and lifecycle
 */

class ExtensionTemplateCache {
    constructor() {
        this.maxCacheSize = 5 * 1024 * 1024; // 5MB max per extension
        this.maxTemplateSize = 1024; // 1KB per template when compressed
        this.cache = new Map();
        this.compressionEnabled = typeof CompressionStream !== 'undefined';
        this.storagePrefix = 'template_';
        this.versionKey = 'template_version';
        this.currentVersion = '1.0.0';
        
        this.init();
    }
    
    async init() {
        await this.validateCacheVersion();
        await this.loadCacheFromStorage();
        this.setupStorageListener();
    }
    
    async validateCacheVersion() {
        try {
            const result = await chrome.storage.local.get(this.versionKey);
            const storedVersion = result[this.versionKey];
            
            if (storedVersion !== this.currentVersion) {
                console.log('Template cache version mismatch, clearing cache');
                await this.clearCache();
                await chrome.storage.local.set({ [this.versionKey]: this.currentVersion });
            }
        } catch (error) {
            console.error('Failed to validate cache version:', error);
        }
    }
    
    async loadCacheFromStorage() {
        try {
            const result = await chrome.storage.local.get(null);
            let totalSize = 0;
            
            for (const [key, value] of Object.entries(result)) {
                if (key.startsWith(this.storagePrefix)) {
                    const templateId = key.replace(this.storagePrefix, '');
                    
                    if (value && value.data && value.timestamp) {
                        const decompressed = await this.decompressData(value.data);
                        this.cache.set(templateId, {
                            data: decompressed,
                            timestamp: value.timestamp,
                            size: value.size || 0
                        });
                        totalSize += value.size || 0;
                    }
                }
            }
            
            console.log(`Loaded ${this.cache.size} templates from storage (${totalSize} bytes)`);
            
            // Check if we need to cleanup due to size limits
            if (totalSize > this.maxCacheSize) {
                await this.evictOldestEntries(totalSize - this.maxCacheSize);
            }
        } catch (error) {
            console.error('Failed to load cache from storage:', error);
        }
    }
    
    setupStorageListener() {
        // Listen for storage changes from background script
        chrome.storage.onChanged.addListener((changes, namespace) => {
            if (namespace === 'local') {
                for (const [key, change] of Object.entries(changes)) {
                    if (key.startsWith(this.storagePrefix)) {
                        const templateId = key.replace(this.storagePrefix, '');
                        
                        if (change.newValue) {
                            this.handleStorageUpdate(templateId, change.newValue);
                        } else {
                            this.cache.delete(templateId);
                        }
                    }
                }
            }
        });
    }
    
    async handleStorageUpdate(templateId, storageValue) {
        try {
            if (storageValue.data && storageValue.timestamp) {
                const decompressed = await this.decompressData(storageValue.data);
                this.cache.set(templateId, {
                    data: decompressed,
                    timestamp: storageValue.timestamp,
                    size: storageValue.size || 0
                });
            }
        } catch (error) {
            console.error(`Failed to handle storage update for ${templateId}:`, error);
        }
    }
    
    async getTemplate(templateId) {
        // Check memory cache first
        if (this.cache.has(templateId)) {
            const cached = this.cache.get(templateId);
            
            // Check if cache entry is still valid (24 hours)
            const maxAge = 24 * 60 * 60 * 1000;
            if (Date.now() - cached.timestamp < maxAge) {
                return cached.data;
            } else {
                // Remove expired entry
                this.cache.delete(templateId);
                await chrome.storage.local.remove(`${this.storagePrefix}${templateId}`);
            }
        }
        
        // Try to load from storage
        try {
            const result = await chrome.storage.local.get(`${this.storagePrefix}${templateId}`);
            const storageValue = result[`${this.storagePrefix}${templateId}`];
            
            if (storageValue && storageValue.data) {
                const decompressed = await this.decompressData(storageValue.data);
                
                // Update memory cache
                this.cache.set(templateId, {
                    data: decompressed,
                    timestamp: storageValue.timestamp,
                    size: storageValue.size || 0
                });
                
                return decompressed;
            }
        } catch (error) {
            console.error(`Failed to load template ${templateId} from storage:`, error);
        }
        
        return null;
    }
    
    async setTemplate(templateId, templateData) {
        try {
            // Validate template size
            const dataString = JSON.stringify(templateData);
            if (dataString.length > this.maxTemplateSize * 10) { // Allow 10x before compression
                console.warn(`Template ${templateId} is too large: ${dataString.length} bytes`);
                return false;
            }
            
            // Compress data
            const compressed = await this.compressData(templateData);
            const compressedSize = compressed.length || compressed.byteLength || 0;
            
            if (compressedSize > this.maxTemplateSize) {
                console.warn(`Compressed template ${templateId} is too large: ${compressedSize} bytes`);
                return false;
            }
            
            // Check total cache size
            const currentSize = await this.getCurrentCacheSize();
            if (currentSize + compressedSize > this.maxCacheSize) {
                await this.evictOldestEntries(compressedSize);
            }
            
            const cacheEntry = {
                data: compressed,
                timestamp: Date.now(),
                size: compressedSize
            };
            
            // Store in extension storage
            await chrome.storage.local.set({
                [`${this.storagePrefix}${templateId}`]: cacheEntry
            });
            
            // Update memory cache
            this.cache.set(templateId, {
                data: templateData,
                timestamp: cacheEntry.timestamp,
                size: compressedSize
            });
            
            return true;
        } catch (error) {
            console.error(`Failed to set template ${templateId}:`, error);
            return false;
        }
    }
    
    async compressData(data) {
        if (!this.compressionEnabled) {
            return JSON.stringify(data);
        }
        
        try {
            const stream = new CompressionStream('gzip');
            const writer = stream.writable.getWriter();
            const reader = stream.readable.getReader();
            
            writer.write(new TextEncoder().encode(JSON.stringify(data)));
            writer.close();
            
            const chunks = [];
            let done = false;
            while (!done) {
                const { value, done: readerDone } = await reader.read();
                done = readerDone;
                if (value) chunks.push(value);
            }
            
            return new Uint8Array(chunks.reduce((acc, chunk) => [...acc, ...chunk], []));
        } catch (error) {
            console.error('Compression failed, falling back to JSON:', error);
            return JSON.stringify(data);
        }
    }
    
    async decompressData(compressedData) {
        if (typeof compressedData === 'string') {
            return JSON.parse(compressedData);
        }
        
        if (!this.compressionEnabled) {
            return JSON.parse(new TextDecoder().decode(compressedData));
        }
        
        try {
            const stream = new DecompressionStream('gzip');
            const writer = stream.writable.getWriter();
            const reader = stream.readable.getReader();
            
            writer.write(compressedData);
            writer.close();
            
            const chunks = [];
            let done = false;
            while (!done) {
                const { value, done: readerDone } = await reader.read();
                done = readerDone;
                if (value) chunks.push(value);
            }
            
            const decompressed = new Uint8Array(chunks.reduce((acc, chunk) => [...acc, ...chunk], []));
            return JSON.parse(new TextDecoder().decode(decompressed));
        } catch (error) {
            console.error('Decompression failed:', error);
            // Try as plain text
            return JSON.parse(new TextDecoder().decode(compressedData));
        }
    }
    
    async getCurrentCacheSize() {
        try {
            const result = await chrome.storage.local.get(null);
            let totalSize = 0;
            
            for (const [key, value] of Object.entries(result)) {
                if (key.startsWith(this.storagePrefix) && value.size) {
                    totalSize += value.size;
                }
            }
            
            return totalSize;
        } catch (error) {
            console.error('Failed to calculate cache size:', error);
            return 0;
        }
    }
    
    async evictOldestEntries(bytesToFree) {
        try {
            // Get all cache entries sorted by timestamp
            const entries = Array.from(this.cache.entries())
                .map(([id, data]) => ({ id, ...data }))
                .sort((a, b) => a.timestamp - b.timestamp);
            
            let freedBytes = 0;
            const toRemove = [];
            
            for (const entry of entries) {
                toRemove.push(entry.id);
                freedBytes += entry.size;
                
                if (freedBytes >= bytesToFree) {
                    break;
                }
            }
            
            // Remove from storage and cache
            for (const templateId of toRemove) {
                await chrome.storage.local.remove(`${this.storagePrefix}${templateId}`);
                this.cache.delete(templateId);
            }
            
            console.log(`Evicted ${toRemove.length} templates, freed ${freedBytes} bytes`);
        } catch (error) {
            console.error('Failed to evict cache entries:', error);
        }
    }
    
    async clearCache() {
        try {
            // Get all template keys
            const result = await chrome.storage.local.get(null);
            const templateKeys = Object.keys(result).filter(key => key.startsWith(this.storagePrefix));
            
            // Remove from storage
            await chrome.storage.local.remove(templateKeys);
            
            // Clear memory cache
            this.cache.clear();
            
            console.log(`Cleared ${templateKeys.length} templates from cache`);
        } catch (error) {
            console.error('Failed to clear cache:', error);
        }
    }
    
    async getTemplateChunks(templateId, chunkSize = 1000) {
        const template = await this.getTemplate(templateId);
        if (!template) return null;
        
        const templateString = typeof template === 'string' ? template : JSON.stringify(template);
        const chunks = [];
        
        for (let i = 0; i < templateString.length; i += chunkSize) {
            chunks.push(templateString.slice(i, i + chunkSize));
        }
        
        return chunks;
    }
    
    getCacheStats() {
        const stats = {
            memoryEntries: this.cache.size,
            memorySize: Array.from(this.cache.values()).reduce((sum, entry) => sum + (entry.size || 0), 0),
            compressionEnabled: this.compressionEnabled,
            version: this.currentVersion
        };
        
        return stats;
    }
}

// Create global instance
window.ExtensionTemplateCache = window.ExtensionTemplateCache || new ExtensionTemplateCache();

// Export for modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ExtensionTemplateCache;
}
