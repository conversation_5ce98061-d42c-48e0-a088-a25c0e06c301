// Simple security helper functions (inline to avoid import issues)
function safeSetHTML(element, html, options = {}) {
  if (!element) return false;
  if (options.trusted === true) {
    element.innerHTML = html;
  } else {
    // Basic sanitization - remove script tags and dangerous attributes
    const sanitized = html
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/\s*on\w+\s*=\s*["'][^"']*["']/gi, '')
      .replace(/href\s*=\s*["']javascript:[^"']*["']/gi, '');
    element.innerHTML = sanitized;
  }
  return true;
}

function validateSearchInput(input) {
  if (typeof input !== 'string') return { isValid: false, errors: ['Input must be string'] };
  if (input.length > 500) return { isValid: false, errors: ['Input too long'] };
  if (/<script|javascript:|on\w+=/i.test(input)) return { isValid: false, errors: ['Invalid characters'] };
  return { isValid: true, value: input.trim() };
}

/**
 * Products Page Manager
 * Handles 5 million product rows with virtual scrolling and efficient rendering
 */

class ProductsPageManager {
  constructor() {
    this.totalProducts = 0;
    this.visibleRows = 50; // Show 50 rows at a time
    this.rowHeight = 60; // Height of each product row
    this.bufferSize = 10; // Extra rows to render for smooth scrolling
    this.currentOffset = 0;
    this.isLoading = false;
    this.searchQuery = '';
    this.filters = {};
    this.sortBy = 'lastSaleDate';
    this.sortOrder = 'desc';

    // Virtual scrolling elements
    this.container = null;
    this.viewport = null;
    this.scrollbar = null;

    // Data management - optimized for Chrome extension memory limits
    this.loadedProducts = new Map(); // Cache loaded product data
    this.maxCacheSize = 1000; // Reduced cache size for extension memory limits
    this.imageCache = new Map(); // Cache product images
    this.maxImageCache = 100; // Reduced image cache for extension memory limits

    // Extension-specific optimizations
    this.timerManager = window.ExtensionTimerManager;
    this.domOptimizer = window.ExtensionDOMOptimizer;
    this.memoryMonitor = window.ExtensionMemoryMonitor;
    this.isExtensionContext = typeof chrome !== 'undefined' && chrome.runtime;

    // Performance tracking
    this.renderTime = 0;
    this.scrollTime = 0;

    // Extension storage integration
    this.storageKey = 'products_cache';
    this.setupExtensionOptimizations();
  }

  /**
   * Setup extension-specific optimizations
   */
  setupExtensionOptimizations() {
    if (!this.isExtensionContext) return;

    // Register memory cleanup strategy
    if (this.memoryMonitor) {
      this.memoryMonitor.registerCleanupStrategy({
        name: 'products-cache-cleanup',
        level: 'warning',
        cleanup: async () => {
          this.clearOldCacheEntries();
        }
      });

      this.memoryMonitor.registerCleanupStrategy({
        name: 'products-aggressive-cleanup',
        level: 'critical',
        cleanup: async () => {
          this.loadedProducts.clear();
          this.imageCache.clear();
        }
      });
    }
  }

  /**
   * Initialize products page with virtual scrolling
   */
  async initializeProductsPage(containerElement) {
    this.container = containerElement;

    try {
      // Get total product count
      this.totalProducts = await this.getTotalProductCount();
      console.log(`📊 Total products: ${this.totalProducts.toLocaleString()}`);

      // Create virtual scrolling structure
      await this.createVirtualScrollStructure();

      // Setup event listeners
      this.setupEventListeners();

      // Load initial data
      await this.loadProductBatch(0, this.visibleRows + this.bufferSize);

      // Render initial view
      this.renderVisibleProducts();

      console.log('✅ Products page initialized with virtual scrolling');

    } catch (error) {
      console.error('❌ Failed to initialize products page:', error);
      throw error;
    }
  }

  /**
   * Create virtual scrolling DOM structure using extension DOM optimizer
   */
  async createVirtualScrollStructure() {
    if (this.domOptimizer) {
      // Use extension DOM optimizer for efficient rendering
      await this.domOptimizer.updateElement(this.container, {
        innerHTML: this.getProductsHTML()
      });
    } else {
      // Fallback to direct innerHTML
      this.container.innerHTML = this.getProductsHTML();
    }

    // Cache viewport elements
    this.viewport = this.container.querySelector('.products-viewport');
    this.visibleArea = this.container.querySelector('.products-visible-area');
  }

  /**
   * Get products HTML template
   */
  getProductsHTML() {
    return `
      <div class="products-header">
        <div class="products-search">
          <input type="text" placeholder="Search products..." class="search-input">
          <div class="products-filters">
            <select class="marketplace-filter">
              <option value="">All Marketplaces</option>
              <option value="US">United States</option>
              <option value="UK">United Kingdom</option>
              <option value="DE">Germany</option>
              <option value="FR">France</option>
              <option value="IT">Italy</option>
              <option value="ES">Spain</option>
              <option value="JP">Japan</option>
            </select>
            <select class="status-filter">
              <option value="">All Status</option>
              <option value="live">Live</option>
              <option value="draft">Draft</option>
              <option value="review">In Review</option>
            </select>
          </div>
        </div>
        <div class="products-stats">
          <span class="total-count">${this.totalProducts.toLocaleString()} products</span>
          <span class="visible-count">Showing 1-${this.visibleRows}</span>
        </div>
      </div>

      <div class="products-table-container">
        <div class="products-table-header">
          <div class="header-cell image">Image</div>
          <div class="header-cell asin">ASIN</div>
          <div class="header-cell title">Title</div>
          <div class="header-cell sales">Sales</div>
          <div class="header-cell returns">Returns</div>
          <div class="header-cell royalties">Royalties</div>
          <div class="header-cell marketplace">Marketplace</div>
          <div class="header-cell status">Status</div>
          <div class="header-cell actions">Actions</div>
        </div>

        <div class="products-viewport" style="height: ${this.visibleRows * this.rowHeight}px; overflow-y: auto;">
          <div class="products-virtual-container" style="height: ${this.totalProducts * this.rowHeight}px; position: relative;">
            <div class="products-visible-area" style="position: absolute; top: 0; width: 100%;"></div>
          </div>
        </div>
      </div>
      
      <div class="products-loading" style="display: none;">
        <div class="loading-spinner"></div>
        <span>Loading products...</span>
      </div>
    `;
    
    this.viewport = this.container.querySelector('.products-viewport');
    this.visibleArea = this.container.querySelector('.products-visible-area');
    this.loadingIndicator = this.container.querySelector('.products-loading');
  }

  /**
   * Setup event listeners for virtual scrolling
   */
  setupEventListeners() {
    // Scroll event with extension-aware throttling
    let scrollTimerId;
    this.viewport.addEventListener('scroll', () => {
      if (scrollTimerId) return;

      if (this.timerManager) {
        scrollTimerId = this.timerManager.setTimeout(() => {
          this.handleScroll();
          scrollTimerId = null;
        }, 16, this.timerManager.timerCategories.UI); // ~60fps
      } else {
        scrollTimerId = setTimeout(() => {
          this.handleScroll();
          scrollTimerId = null;
        }, 16);
      }
    }, { passive: true });

    // Search input with extension-aware debouncing
    const searchInput = this.container.querySelector('.search-input');
    let searchTimerId;
    searchInput.addEventListener('input', (e) => {
      if (searchTimerId) {
        if (this.timerManager) {
          this.timerManager.clearTimeout(searchTimerId);
        } else {
          clearTimeout(searchTimerId);
        }
      }

      if (this.timerManager) {
        searchTimerId = this.timerManager.setTimeout(() => {
          this.handleSearch(e.target.value);
        }, 300, this.timerManager.timerCategories.BACKGROUND);
      } else {
        searchTimerId = setTimeout(() => {
          this.handleSearch(e.target.value);
        }, 300);
      }
    });
    
    // Filter changes
    const filters = this.container.querySelectorAll('.marketplace-filter, .status-filter');
    filters.forEach(filter => {
      filter.addEventListener('change', () => {
        this.handleFilterChange();
      });
    });
  }

  /**
   * Handle scroll events for virtual scrolling
   */
  async handleScroll() {
    const scrollTop = this.viewport.scrollTop;
    const newOffset = Math.floor(scrollTop / this.rowHeight);
    
    // Only update if we've scrolled significantly
    if (Math.abs(newOffset - this.currentOffset) < 5) return;
    
    this.currentOffset = newOffset;
    
    // Calculate which products to load
    const startIndex = Math.max(0, newOffset - this.bufferSize);
    const endIndex = Math.min(this.totalProducts, newOffset + this.visibleRows + this.bufferSize);
    
    // Load products if not in cache
    await this.loadProductBatch(startIndex, endIndex - startIndex);
    
    // Render visible products
    this.renderVisibleProducts();
    
    // Update visible count
    this.updateVisibleCount();
  }

  /**
   * Load a batch of products
   */
  async loadProductBatch(startIndex, count) {
    if (this.isLoading) return;
    
    const endIndex = startIndex + count;
    const needsLoading = [];
    
    // Check which products are not in cache
    for (let i = startIndex; i < endIndex; i++) {
      if (!this.loadedProducts.has(i)) {
        needsLoading.push(i);
      }
    }
    
    if (needsLoading.length === 0) return;
    
    this.isLoading = true;
    this.showLoading(true);
    
    try {
      // Load products from IndexedDB with pagination
      const products = await this.loadProductsFromDB(startIndex, count);
      
      // Cache loaded products
      products.forEach((product, index) => {
        this.cacheProduct(startIndex + index, product);
      });
      
      console.log(`📦 Loaded ${products.length} products (${startIndex}-${endIndex})`);
      
    } catch (error) {
      console.error('❌ Failed to load product batch:', error);
    } finally {
      this.isLoading = false;
      this.showLoading(false);
    }
  }

  /**
   * Load products from IndexedDB with efficient querying
   */
  async loadProductsFromDB(offset, limit) {
    try {
      const transaction = window.IndexedDBManager.db.transaction(['listingsData'], 'readonly');
      const store = transaction.objectStore('listingsData');
      
      const products = [];
      let cursor = await store.openCursor();
      let currentIndex = 0;
      
      // Skip to offset
      if (offset > 0 && cursor) {
        await cursor.advance(offset);
      }
      
      // Collect products
      while (cursor && products.length < limit) {
        const product = cursor.value;
        
        // Apply filters if any
        if (this.matchesFilters(product)) {
          products.push(product);
        }
        
        await cursor.continue();
      }
      
      return products;
      
    } catch (error) {
      console.error('❌ Failed to load products from DB:', error);
      return [];
    }
  }

  /**
   * Check if product matches current filters
   */
  matchesFilters(product) {
    // Search query
    if (this.searchQuery) {
      const query = this.searchQuery.toLowerCase();
      if (!product.title?.toLowerCase().includes(query) && 
          !product.asin?.toLowerCase().includes(query)) {
        return false;
      }
    }
    
    // Marketplace filter
    if (this.filters.marketplace && product.marketplace !== this.filters.marketplace) {
      return false;
    }
    
    // Status filter
    if (this.filters.status && product.status !== this.filters.status) {
      return false;
    }
    
    return true;
  }

  /**
   * Render visible products in the viewport
   */
  renderVisibleProducts() {
    const startTime = performance.now();
    
    const startIndex = Math.max(0, this.currentOffset - this.bufferSize);
    const endIndex = Math.min(this.totalProducts, this.currentOffset + this.visibleRows + this.bufferSize);
    
    const html = [];
    
    for (let i = startIndex; i < endIndex; i++) {
      const product = this.loadedProducts.get(i);
      if (product) {
        html.push(this.renderProductRow(product, i));
      } else {
        html.push(this.renderPlaceholderRow(i));
      }
    }
    
    // Update visible area position and content
    this.visibleArea.style.top = `${startIndex * this.rowHeight}px`;
    this.visibleArea.innerHTML = html.join('');
    
    // Load images for visible products
    this.loadVisibleImages();
    
    this.renderTime = performance.now() - startTime;
  }

  /**
   * Render a single product row
   */
  renderProductRow(product, index) {
    const metrics = this.getProductMetrics(product);
    
    return `
      <div class="product-row" data-index="${index}" style="height: ${this.rowHeight}px;">
        <div class="cell image">
          <img class="product-image" data-asin="${product.asin}" src="placeholder.jpg" alt="Product">
        </div>
        <div class="cell asin">${product.asin}</div>
        <div class="cell title" title="${product.title}">${this.truncateText(product.title, 50)}</div>
        <div class="cell sales">${metrics.totalSales || 0}</div>
        <div class="cell returns">${metrics.totalReturns || 0}</div>
        <div class="cell royalties">$${(metrics.totalRoyalties || 0).toFixed(2)}</div>
        <div class="cell marketplace">
          <img src="./assets/${product.marketplace}.svg" alt="${product.marketplace}" width="20">
        </div>
        <div class="cell status">
          <span class="status-badge ${product.status}">${product.status}</span>
        </div>
        <div class="cell actions">
          <button class="action-btn view" data-asin="${product.asin}">View</button>
          <button class="action-btn edit" data-asin="${product.asin}">Edit</button>
        </div>
      </div>
    `;
  }

  /**
   * Render placeholder row for loading products
   */
  renderPlaceholderRow(index) {
    return `
      <div class="product-row placeholder" data-index="${index}" style="height: ${this.rowHeight}px;">
        <div class="cell image"><div class="skeleton"></div></div>
        <div class="cell asin"><div class="skeleton"></div></div>
        <div class="cell title"><div class="skeleton"></div></div>
        <div class="cell sales"><div class="skeleton"></div></div>
        <div class="cell returns"><div class="skeleton"></div></div>
        <div class="cell royalties"><div class="skeleton"></div></div>
        <div class="cell marketplace"><div class="skeleton"></div></div>
        <div class="cell status"><div class="skeleton"></div></div>
        <div class="cell actions"><div class="skeleton"></div></div>
      </div>
    `;
  }

  /**
   * Cache product with size limit
   */
  cacheProduct(index, product) {
    if (this.loadedProducts.size >= this.maxCacheSize) {
      // Remove oldest entries
      const oldestKey = this.loadedProducts.keys().next().value;
      this.loadedProducts.delete(oldestKey);
    }
    
    this.loadedProducts.set(index, product);
  }

  /**
   * Get total product count
   */
  async getTotalProductCount() {
    try {
      const transaction = window.IndexedDBManager.db.transaction(['listingsData'], 'readonly');
      const store = transaction.objectStore('listingsData');
      return await store.count();
    } catch (error) {
      console.error('❌ Failed to get total product count:', error);
      return 0;
    }
  }

  /**
   * Clear old cache entries based on timestamp
   */
  clearOldCacheEntries() {
    const cutoffTime = Date.now() - (30 * 60 * 1000); // 30 minutes

    for (const [key, value] of this.loadedProducts) {
      if (value.timestamp && value.timestamp < cutoffTime) {
        this.loadedProducts.delete(key);
      }
    }

    for (const [key, value] of this.imageCache) {
      if (value.timestamp && value.timestamp < cutoffTime) {
        this.imageCache.delete(key);
      }
    }

    console.log('Cleared old cache entries');
  }

  /**
   * Handle memory pressure by reducing cache sizes
   */
  handleMemoryPressure(level) {
    switch (level) {
      case 'emergency':
        this.loadedProducts.clear();
        this.imageCache.clear();
        this.maxCacheSize = 100;
        this.maxImageCache = 10;
        break;
      case 'critical':
        this.clearOldCacheEntries();
        this.maxCacheSize = 500;
        this.maxImageCache = 50;
        break;
      case 'warning':
        this.clearOldCacheEntries();
        break;
    }
  }

  /**
   * Save cache to extension storage
   */
  async saveCacheToStorage() {
    if (!this.isExtensionContext) return;

    try {
      const cacheData = {
        products: Array.from(this.loadedProducts.entries()).slice(0, 100), // Limit storage
        timestamp: Date.now()
      };

      await chrome.storage.local.set({
        [this.storageKey]: cacheData
      });
    } catch (error) {
      console.error('Failed to save cache to storage:', error);
    }
  }

  /**
   * Load cache from extension storage
   */
  async loadCacheFromStorage() {
    if (!this.isExtensionContext) return;

    try {
      const result = await chrome.storage.local.get(this.storageKey);
      const cacheData = result[this.storageKey];

      if (cacheData && cacheData.products) {
        // Check if cache is not too old (1 hour)
        const maxAge = 60 * 60 * 1000;
        if (Date.now() - cacheData.timestamp < maxAge) {
          this.loadedProducts = new Map(cacheData.products);
          console.log('Loaded products cache from storage');
        }
      }
    } catch (error) {
      console.error('Failed to load cache from storage:', error);
    }
  }

  /**
   * Get product metrics (cached or calculated)
   */
  getProductMetrics(product) {
    // This would fetch from productMetrics store or calculate
    return {
      totalSales: Math.floor(Math.random() * 1000),
      totalReturns: Math.floor(Math.random() * 50),
      totalRoyalties: Math.random() * 500
    };
  }

  /**
   * Utility functions
   */
  truncateText(text, maxLength) {
    return text && text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
  }

  showLoading(show) {
    this.loadingIndicator.style.display = show ? 'flex' : 'none';
  }

  updateVisibleCount() {
    const start = this.currentOffset + 1;
    const end = Math.min(this.currentOffset + this.visibleRows, this.totalProducts);
    const visibleCount = this.container.querySelector('.visible-count');
    if (visibleCount) {
      visibleCount.textContent = `Showing ${start.toLocaleString()}-${end.toLocaleString()}`;
    }
  }

  async handleSearch(query) {
    this.searchQuery = query;
    this.currentOffset = 0;
    this.loadedProducts.clear();
    await this.loadProductBatch(0, this.visibleRows + this.bufferSize);
    this.renderVisibleProducts();
  }

  async handleFilterChange() {
    const marketplaceFilter = this.container.querySelector('.marketplace-filter');
    const statusFilter = this.container.querySelector('.status-filter');
    
    this.filters = {
      marketplace: marketplaceFilter.value,
      status: statusFilter.value
    };
    
    this.currentOffset = 0;
    this.loadedProducts.clear();
    await this.loadProductBatch(0, this.visibleRows + this.bufferSize);
    this.renderVisibleProducts();
  }

  loadVisibleImages() {
    // Load product images for visible rows
    const images = this.visibleArea.querySelectorAll('.product-image');
    images.forEach(img => {
      const asin = img.dataset.asin;
      if (asin && !this.imageCache.has(asin)) {
        this.loadProductImage(asin, img);
      }
    });
  }

  async loadProductImage(asin, imgElement) {
    try {
      // This would load from productImages store or fetch from Amazon
      // For now, use placeholder
      imgElement.src = `https://via.placeholder.com/60x60?text=${asin}`;
      this.imageCache.set(asin, imgElement.src);
    } catch (error) {
      console.error(`❌ Failed to load image for ${asin}:`, error);
    }
  }
}

// Global instance
window.ProductsPageManager = new ProductsPageManager();

// Export for global use
window.initializeProductsPage = (container) => window.ProductsPageManager.initializeProductsPage(container);
