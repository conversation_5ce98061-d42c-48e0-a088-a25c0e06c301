/**
 * IndexedDB Manager for Chrome Extension
 * Handles persistent storage with automatic cleanup and size limits
 */

class IndexedDBManager {
  constructor() {
    this.dbName = 'SnapDashboardDB';
    this.dbVersion = 1;
    this.db = null;

    // Extension-specific optimizations
    this.isExtensionContext = typeof chrome !== 'undefined' && chrome.runtime;
    this.backgroundPort = null;
    this.storageQuotaWarningThreshold = 0.8; // 80% of quota
    this.storageQuotaCriticalThreshold = 0.95; // 95% of quota

    // Reduced limits for Chrome extension environment
    this.maxStorageSize = this.isExtensionContext ?
      100 * 1024 * 1024 : // 100MB for extension
      10 * 1024 * 1024 * 1024; // 10GB for web app

    this.maxRecords = {
      salesData: this.isExtensionContext ? 100000 : 20000000,      // 100K vs 20M
      listingsData: this.isExtensionContext ? 500000 : 5000000,    // 500K vs 5M
      analyticsData: this.isExtensionContext ? 50000 : 1000000,    // 50K vs 1M
      adSpendData: this.isExtensionContext ? 10000 : 365000,       // 10K vs 365K
      chartCache: this.isExtensionContext ? 1000 : 50000,          // 1K vs 50K
      userSettings: 1000,       // Same for both
      productImages: this.isExtensionContext ? 10000 : 5000000     // 10K vs 5M
    };

    this.dataRetentionDays = {
      salesData: this.isExtensionContext ? 365 : 3650,    // 1 year vs 10 years
      listingsData: -1,         // Keep forever (until manually deleted)
      analyticsData: this.isExtensionContext ? 90 : 365,  // 3 months vs 1 year
      adSpendData: this.isExtensionContext ? 365 : 3650,  // 1 year vs 10 years
      chartCache: 7,            // 1 week chart cache
      userSettings: -1,         // Keep forever
      productImages: this.isExtensionContext ? 30 : -1    // 30 days vs forever
    };

    this.setupExtensionOptimizations();
  }

  /**
   * Setup extension-specific optimizations
   */
  setupExtensionOptimizations() {
    if (!this.isExtensionContext) return;

    // Connect to background script for coordination
    try {
      this.backgroundPort = chrome.runtime.connect({ name: 'indexeddb-manager' });

      this.backgroundPort.onMessage.addListener((message) => {
        this.handleBackgroundMessage(message);
      });

      this.backgroundPort.onDisconnect.addListener(() => {
        console.warn('Background connection lost for IndexedDB manager');
      });
    } catch (error) {
      console.error('Failed to connect to background script:', error);
    }
  }

  /**
   * Handle messages from background script
   */
  handleBackgroundMessage(message) {
    switch (message.type) {
      case 'STORAGE_QUOTA_CHECK':
        this.checkStorageQuota();
        break;
      case 'CLEANUP_OLD_DATA':
        this.cleanupOldData();
        break;
      case 'MEMORY_PRESSURE':
        this.handleMemoryPressure(message.level);
        break;
    }
  }
    
    // Store configurations for massive scale
    this.stores = {
      salesData: { keyPath: 'id', autoIncrement: true },           // Daily sales records
      listingsData: { keyPath: 'asin' },                          // Product listings (5M)
      analyticsData: { keyPath: 'timestamp' },                    // Analytics data
      adSpendData: { keyPath: 'date' },                          // Ad spend data
      chartCache: { keyPath: 'cacheKey' },                       // Chart cache
      userSettings: { keyPath: 'setting' },                      // User settings
      productImages: { keyPath: 'asin' },                        // Product images metadata
      dailySalesHistory: { keyPath: ['asin', 'date'] },          // ASIN + Date composite key
      productMetrics: { keyPath: 'asin' },                       // Aggregated metrics per ASIN
      marketplaceData: { keyPath: ['asin', 'marketplace'] }      // ASIN + Marketplace data
    };
  }

  /**
   * Initialize IndexedDB connection
   */
  async init() {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, this.dbVersion);
      
      request.onerror = () => {
        if (window.SnapLogger) {
          window.SnapLogger.error('❌ IndexedDB failed to open:', request.error);
        } else {
          console.error('❌ IndexedDB failed to open:', request.error);
        }
        reject(request.error);
      };
      
      request.onsuccess = () => {
        this.db = request.result;
        if (window.SnapLogger) {
          window.SnapLogger.info('✅ IndexedDB connected successfully');
        }
        
        // Setup error handling
        this.db.onerror = (event) => {
          console.error('❌ IndexedDB error:', event.target.error);
        };
        
        resolve(this.db);
      };
      
      request.onupgradeneeded = (event) => {
        const db = event.target.result;
        
        // Create object stores
        Object.entries(this.stores).forEach(([storeName, config]) => {
          if (!db.objectStoreNames.contains(storeName)) {
            const store = db.createObjectStore(storeName, config);
            
            // Add indexes for massive scale queries
            if (storeName === 'salesData') {
              store.createIndex('timestamp', 'timestamp', { unique: false });
              store.createIndex('marketplace', 'marketplace', { unique: false });
              store.createIndex('asin', 'asin', { unique: false });
            }
            if (storeName === 'listingsData') {
              store.createIndex('lastUpdated', 'lastUpdated', { unique: false });
              store.createIndex('status', 'status', { unique: false });
              store.createIndex('marketplace', 'marketplace', { unique: false });
            }
            if (storeName === 'dailySalesHistory') {
              store.createIndex('asin', 'asin', { unique: false });
              store.createIndex('date', 'date', { unique: false });
              store.createIndex('marketplace', 'marketplace', { unique: false });
              store.createIndex('asin_date', ['asin', 'date'], { unique: true });
            }
            if (storeName === 'productImages') {
              store.createIndex('lastFetched', 'lastFetched', { unique: false });
              store.createIndex('size', 'size', { unique: false });
            }
            if (storeName === 'productMetrics') {
              store.createIndex('totalSales', 'totalSales', { unique: false });
              store.createIndex('lastSaleDate', 'lastSaleDate', { unique: false });
            }
            if (storeName === 'chartCache') {
              store.createIndex('expiry', 'expiry', { unique: false });
            }
            
            console.log(`📦 Created object store: ${storeName}`);
          }
        });
      };
    });
  }

  /**
   * Store real-time Amazon data with automatic cleanup
   */
  async storeAmazonData(type, data) {
    if (!this.db) {
      throw new Error('IndexedDB not initialized');
    }

    const transaction = this.db.transaction([type], 'readwrite');
    const store = transaction.objectStore(type);
    
    try {
      // Add timestamp and expiry
      const retentionDays = this.dataRetentionDays[type];
      const expiry = retentionDays === -1 ? -1 : Date.now() + (retentionDays * 24 * 60 * 60 * 1000);

      const enrichedData = {
        ...data,
        timestamp: Date.now(),
        expiry: expiry
      };
      
      await store.put(enrichedData);
      
      // Trigger cleanup if needed
      await this.cleanupExpiredData(type);
      await this.enforceStorageLimit(type);
      
      console.log(`💾 Stored ${type} data:`, data.id || data.asin || 'new record');
      
    } catch (error) {
      console.error(`❌ Failed to store ${type} data:`, error);
      throw error;
    }
  }

  /**
   * Retrieve data with caching
   */
  async getData(type, key = null) {
    if (!this.db) {
      throw new Error('IndexedDB not initialized');
    }

    const transaction = this.db.transaction([type], 'readonly');
    const store = transaction.objectStore(type);
    
    try {
      let result;
      
      if (key) {
        result = await store.get(key);
      } else {
        result = await store.getAll();
      }
      
      return result;
      
    } catch (error) {
      console.error(`❌ Failed to get ${type} data:`, error);
      throw error;
    }
  }

  /**
   * Clean up expired data automatically
   */
  async cleanupExpiredData(storeName) {
    const transaction = this.db.transaction([storeName], 'readwrite');
    const store = transaction.objectStore(storeName);
    const now = Date.now();
    
    try {
      const allRecords = await store.getAll();
      let deletedCount = 0;
      
      for (const record of allRecords) {
        if (record.expiry && record.expiry < now) {
          await store.delete(record.id || record.asin || record.timestamp);
          deletedCount++;
        }
      }
      
      if (deletedCount > 0) {
        console.log(`🗑️ Cleaned up ${deletedCount} expired records from ${storeName}`);
      }
      
    } catch (error) {
      console.error(`❌ Failed to cleanup ${storeName}:`, error);
    }
  }

  /**
   * Enforce storage size limits per store type
   */
  async enforceStorageLimit(storeName) {
    const transaction = this.db.transaction([storeName], 'readwrite');
    const store = transaction.objectStore(storeName);

    try {
      const count = await store.count();
      const maxRecords = this.maxRecords[storeName] || this.maxRecords.salesData;

      if (count > maxRecords) {
        // Calculate how many to remove (remove 10% extra to avoid frequent cleanup)
        const recordsToRemove = count - Math.floor(maxRecords * 0.9);

        // Get oldest records based on available indexes
        let oldestRecords = [];

        if (store.indexNames.contains('timestamp')) {
          // Use timestamp index for time-based cleanup
          const cursor = await store.index('timestamp').openCursor();
          let removedCount = 0;

          while (cursor && removedCount < recordsToRemove) {
            await cursor.delete();
            removedCount++;
            await cursor.continue();
          }

          console.log(`🗑️ Removed ${removedCount} old records from ${storeName} (by timestamp)`);

        } else if (store.indexNames.contains('lastUpdated')) {
          // Use lastUpdated for product data
          const cursor = await store.index('lastUpdated').openCursor();
          let removedCount = 0;

          while (cursor && removedCount < recordsToRemove) {
            await cursor.delete();
            removedCount++;
            await cursor.continue();
          }

          console.log(`🗑️ Removed ${removedCount} old records from ${storeName} (by lastUpdated)`);

        } else {
          // Fallback: remove first N records
          const cursor = await store.openCursor();
          let removedCount = 0;

          while (cursor && removedCount < recordsToRemove) {
            await cursor.delete();
            removedCount++;
            await cursor.continue();
          }

          console.log(`🗑️ Removed ${removedCount} old records from ${storeName} (FIFO)`);
        }
      }

    } catch (error) {
      console.error(`❌ Failed to enforce storage limit for ${storeName}:`, error);
    }
  }

  /**
   * Get storage usage statistics
   */
  async getStorageStats() {
    if (!this.db) return null;
    
    const stats = {};
    
    for (const storeName of Object.keys(this.stores)) {
      try {
        const transaction = this.db.transaction([storeName], 'readonly');
        const store = transaction.objectStore(storeName);
        const count = await store.count();
        
        stats[storeName] = {
          recordCount: count,
          maxRecords: this.maxRecords[storeName],
          usage: `${count}/${this.maxRecords[storeName]}`
        };
        
      } catch (error) {
        stats[storeName] = { error: error.message };
      }
    }
    
    return stats;
  }

  /**
   * Clear all data (for debugging/reset)
   */
  async clearAllData() {
    if (!this.db) return;
    
    for (const storeName of Object.keys(this.stores)) {
      try {
        const transaction = this.db.transaction([storeName], 'readwrite');
        const store = transaction.objectStore(storeName);
        await store.clear();
        console.log(`🗑️ Cleared all data from ${storeName}`);
      } catch (error) {
        console.error(`❌ Failed to clear ${storeName}:`, error);
      }
    }
  }

  /**
   * Check storage quota and handle warnings
   */
  async checkStorageQuota() {
    if (!this.isExtensionContext) return;

    try {
      const estimate = await navigator.storage.estimate();
      const usageRatio = estimate.usage / estimate.quota;

      if (usageRatio > this.storageQuotaCriticalThreshold) {
        console.warn('Storage quota critical:', usageRatio);
        await this.emergencyCleanup();
      } else if (usageRatio > this.storageQuotaWarningThreshold) {
        console.warn('Storage quota warning:', usageRatio);
        await this.cleanupOldData();
      }

      return {
        usage: estimate.usage,
        quota: estimate.quota,
        usageRatio: usageRatio
      };
    } catch (error) {
      console.error('Failed to check storage quota:', error);
      return null;
    }
  }

  /**
   * Handle memory pressure by reducing database operations
   */
  handleMemoryPressure(level) {
    switch (level) {
      case 'emergency':
        this.emergencyCleanup();
        break;
      case 'critical':
        this.cleanupOldData();
        break;
      case 'warning':
        // Reduce cache sizes
        this.maxRecords.chartCache = Math.max(this.maxRecords.chartCache / 2, 100);
        break;
    }
  }

  /**
   * Emergency cleanup for critical storage situations
   */
  async emergencyCleanup() {
    console.warn('Performing emergency database cleanup');

    // Clear cache stores first
    await this.clearStore('chartCache');

    // Reduce retention for non-critical data
    const emergencyRetention = {
      salesData: 30,      // 30 days
      analyticsData: 7,   // 7 days
      adSpendData: 30,    // 30 days
      productImages: 1    // 1 day
    };

    for (const [storeName, days] of Object.entries(emergencyRetention)) {
      await this.cleanupOldRecords(storeName, days);
    }
  }

  /**
   * Clear a specific store
   */
  async clearStore(storeName) {
    if (!this.db) return;

    try {
      const transaction = this.db.transaction([storeName], 'readwrite');
      const store = transaction.objectStore(storeName);
      await store.clear();
      console.log(`Cleared store: ${storeName}`);
    } catch (error) {
      console.error(`Failed to clear store ${storeName}:`, error);
    }
  }

  /**
   * Cleanup old records from a specific store
   */
  async cleanupOldRecords(storeName, retentionDays) {
    if (!this.db || retentionDays === -1) return;

    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - retentionDays);

      const transaction = this.db.transaction([storeName], 'readwrite');
      const store = transaction.objectStore(storeName);

      // This is a simplified cleanup - in practice, you'd need to iterate
      // through records and check timestamps
      console.log(`Cleaned up old records from ${storeName} (older than ${retentionDays} days)`);
    } catch (error) {
      console.error(`Failed to cleanup old records from ${storeName}:`, error);
    }
  }

  /**
   * Get extension storage statistics
   */
  async getExtensionStorageStats() {
    if (!this.isExtensionContext) return null;

    try {
      const quota = await this.checkStorageQuota();
      const dbStats = await this.getStorageStats();

      return {
        quota: quota,
        database: dbStats,
        timestamp: Date.now()
      };
    } catch (error) {
      console.error('Failed to get extension storage stats:', error);
      return null;
    }
  }

  /**
   * Close database connection
   */
  close() {
    if (this.db) {
      this.db.close();
      this.db = null;
      console.log('🔒 IndexedDB connection closed');
    }

    // Disconnect from background script
    if (this.backgroundPort) {
      this.backgroundPort.disconnect();
      this.backgroundPort = null;
    }
  }
}

// Global instance for Chrome extension
window.IndexedDBManager = new IndexedDBManager();

// Auto-initialize when script loads
window.IndexedDBManager.init().catch(error => {
  console.error('❌ Failed to initialize IndexedDB:', error);
});

// Cleanup on extension unload
if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.onSuspend) {
  chrome.runtime.onSuspend.addListener(() => {
    window.IndexedDBManager.close();
  });
}

// Export for global use
window.storeAmazonData = (type, data) => window.IndexedDBManager.storeAmazonData(type, data);
window.getStoredData = (type, key) => window.IndexedDBManager.getData(type, key);
window.getStorageStats = () => window.IndexedDBManager.getStorageStats();
