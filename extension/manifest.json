{"manifest_version": 3, "name": "Snap for Merch on Demand", "version": "1.0.0", "description": "Chrome extension for merch dashboard optimization", "permissions": ["storage", "activeTab", "scripting", "system.memory", "alarms", "background"], "host_permissions": ["http://*/*", "https://*/*"], "background": {"service_worker": "background.js", "type": "module"}, "content_scripts": [{"matches": ["<all_urls>"], "js": ["utils/extension-timer-manager.js", "utils/extension-dom-optimizer.js", "utils/extension-memory-monitor.js", "utils/extension-template-cache.js", "performance-optimizations/event-cleanup-manager.js", "performance-optimizations/dom-optimizer.js", "performance-optimizations/memory-monitor.js", "performance-optimizations/indexeddb-manager.js", "performance-optimizations/products-page-manager.js", "performance-optimizations/realtime-data-manager.js", "performance-optimizations/async-data-worker.js", "components/dashboard/dashboard-templates.js", "components/dashboard/dashboard.js", "components/charts/snap-charts.js", "utils/mock-zero-data.js", "utils/content-class-manager.js", "snapapp.js"], "css": ["styles/dashboard.css", "styles/components.css"], "run_at": "document_end"}], "web_accessible_resources": [{"resources": ["assets/*", "images/*", "styles/*", "components/*", "utils/*", "performance-optimizations/*"], "matches": ["<all_urls>"]}], "content_security_policy": {"extension_pages": "script-src 'self'; object-src 'self'; style-src 'self' 'unsafe-inline';"}, "action": {"default_popup": "popup.html", "default_title": "Snap Dashboard"}, "options_page": "options.html", "storage": {"managed_schema": "storage-schema.json"}, "minimum_chrome_version": "88"}