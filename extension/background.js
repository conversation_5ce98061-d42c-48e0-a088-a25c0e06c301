/**
 * Chrome Extension Background Service Worker
 * Optimized for extension lifecycle and memory management
 */

class ExtensionBackgroundManager {
    constructor() {
        this.memoryThresholds = {
            warning: 50 * 1024 * 1024,    // 50MB
            critical: 100 * 1024 * 1024,  // 100MB
            emergency: 150 * 1024 * 1024  // 150MB
        };
        this.persistentTimers = new Map();
        this.contentScriptPorts = new Map();
        this.performanceMetrics = {
            memoryUsage: 0,
            timerCount: 0,
            cacheSize: 0,
            lastCleanup: Date.now()
        };
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.initializeStorage();
        this.startMemoryMonitoring();
        this.setupAlarmHandlers();
    }
    
    setupEventListeners() {
        // Extension lifecycle events
        chrome.runtime.onStartup.addListener(() => this.handleStartup());
        chrome.runtime.onInstalled.addListener(() => this.handleInstall());
        chrome.runtime.onSuspend.addListener(() => this.handleSuspend());
        
        // Content script communication
        chrome.runtime.onConnect.addListener((port) => this.handleConnection(port));
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            this.handleMessage(message, sender, sendResponse);
            return true; // Keep message channel open for async response
        });
        
        // Memory pressure events
        if (chrome.system && chrome.system.memory) {
            chrome.system.memory.onChanged.addListener((info) => this.handleMemoryChange(info));
        }
    }
    
    async initializeStorage() {
        try {
            const result = await chrome.storage.local.get(['extensionState', 'performanceMetrics']);
            if (result.extensionState) {
                await this.restoreState(result.extensionState);
            }
            if (result.performanceMetrics) {
                this.performanceMetrics = { ...this.performanceMetrics, ...result.performanceMetrics };
            }
        } catch (error) {
            console.error('Failed to initialize storage:', error);
        }
    }
    
    startMemoryMonitoring() {
        // Monitor memory every 30 seconds
        chrome.alarms.create('memoryMonitor', { periodInMinutes: 0.5 });
        
        chrome.alarms.onAlarm.addListener((alarm) => {
            if (alarm.name === 'memoryMonitor') {
                this.checkMemoryUsage();
            }
        });
    }
    
    async checkMemoryUsage() {
        try {
            if (chrome.system && chrome.system.memory) {
                const memInfo = await chrome.system.memory.getInfo();
                this.performanceMetrics.memoryUsage = memInfo.availableCapacity;
                
                if (memInfo.availableCapacity < this.memoryThresholds.emergency) {
                    await this.handleMemoryPressure('emergency');
                } else if (memInfo.availableCapacity < this.memoryThresholds.critical) {
                    await this.handleMemoryPressure('critical');
                } else if (memInfo.availableCapacity < this.memoryThresholds.warning) {
                    await this.handleMemoryPressure('warning');
                }
            }
        } catch (error) {
            console.error('Memory monitoring failed:', error);
        }
    }
    
    async handleMemoryPressure(level) {
        console.warn(`Memory pressure detected: ${level}`);
        
        switch (level) {
            case 'emergency':
                await this.emergencyCleanup();
                break;
            case 'critical':
                await this.criticalCleanup();
                break;
            case 'warning':
                await this.warningCleanup();
                break;
        }
        
        // Notify content scripts
        this.broadcastToContentScripts({
            type: 'MEMORY_PRESSURE',
            level: level,
            timestamp: Date.now()
        });
    }
    
    async emergencyCleanup() {
        // Clear all caches
        await chrome.storage.local.clear();
        
        // Cancel non-critical alarms
        const alarms = await chrome.alarms.getAll();
        for (const alarm of alarms) {
            if (!alarm.name.includes('critical')) {
                chrome.alarms.clear(alarm.name);
            }
        }
        
        // Force garbage collection if available
        if (window.gc) {
            window.gc();
        }
    }
    
    async criticalCleanup() {
        // Clear template cache
        await chrome.storage.local.remove(['templateCache', 'chartCache']);
        
        // Reduce timer frequency
        this.reduceTimerFrequency(0.5);
    }
    
    async warningCleanup() {
        // Clear old cache entries
        const result = await chrome.storage.local.get(null);
        const cutoffTime = Date.now() - (24 * 60 * 60 * 1000); // 24 hours
        
        for (const [key, value] of Object.entries(result)) {
            if (value.timestamp && value.timestamp < cutoffTime) {
                await chrome.storage.local.remove(key);
            }
        }
    }
    
    setupAlarmHandlers() {
        chrome.alarms.onAlarm.addListener((alarm) => {
            if (this.persistentTimers.has(alarm.name)) {
                const timerInfo = this.persistentTimers.get(alarm.name);
                this.executeTimerCallback(timerInfo);
            }
        });
    }
    
    async createPersistentTimer(id, callback, intervalMinutes, options = {}) {
        const timerInfo = {
            id,
            callback: callback.toString(),
            intervalMinutes,
            options,
            created: Date.now()
        };
        
        this.persistentTimers.set(id, timerInfo);
        
        await chrome.alarms.create(id, {
            delayInMinutes: options.delayInMinutes || 0,
            periodInMinutes: intervalMinutes
        });
        
        // Persist timer info
        await chrome.storage.local.set({
            [`timer_${id}`]: timerInfo
        });
    }
    
    async executeTimerCallback(timerInfo) {
        try {
            // Reconstruct function from string
            const func = new Function('return ' + timerInfo.callback)();
            await func();
        } catch (error) {
            console.error(`Timer callback failed for ${timerInfo.id}:`, error);
        }
    }
    
    handleConnection(port) {
        const tabId = port.sender?.tab?.id;
        if (tabId) {
            this.contentScriptPorts.set(tabId, port);
            
            port.onDisconnect.addListener(() => {
                this.contentScriptPorts.delete(tabId);
            });
        }
    }
    
    async handleMessage(message, sender, sendResponse) {
        try {
            switch (message.type) {
                case 'GET_TEMPLATE_CACHE':
                    const cache = await this.getTemplateCache(message.templateId);
                    sendResponse({ success: true, data: cache });
                    break;
                    
                case 'SET_TEMPLATE_CACHE':
                    await this.setTemplateCache(message.templateId, message.data);
                    sendResponse({ success: true });
                    break;
                    
                case 'CREATE_PERSISTENT_TIMER':
                    await this.createPersistentTimer(
                        message.id,
                        message.callback,
                        message.intervalMinutes,
                        message.options
                    );
                    sendResponse({ success: true });
                    break;
                    
                case 'PERFORMANCE_METRICS':
                    sendResponse({ success: true, data: this.performanceMetrics });
                    break;
                    
                default:
                    sendResponse({ success: false, error: 'Unknown message type' });
            }
        } catch (error) {
            console.error('Message handling failed:', error);
            sendResponse({ success: false, error: error.message });
        }
    }
    
    async getTemplateCache(templateId) {
        const result = await chrome.storage.local.get(`template_${templateId}`);
        return result[`template_${templateId}`] || null;
    }
    
    async setTemplateCache(templateId, data) {
        const compressed = await this.compressData(data);
        await chrome.storage.local.set({
            [`template_${templateId}`]: {
                data: compressed,
                timestamp: Date.now(),
                size: compressed.length
            }
        });
    }
    
    async compressData(data) {
        // Use built-in compression if available
        if (typeof CompressionStream !== 'undefined') {
            const stream = new CompressionStream('gzip');
            const writer = stream.writable.getWriter();
            const reader = stream.readable.getReader();
            
            writer.write(new TextEncoder().encode(JSON.stringify(data)));
            writer.close();
            
            const chunks = [];
            let done = false;
            while (!done) {
                const { value, done: readerDone } = await reader.read();
                done = readerDone;
                if (value) chunks.push(value);
            }
            
            return new Uint8Array(chunks.reduce((acc, chunk) => [...acc, ...chunk], []));
        }
        
        // Fallback to JSON string
        return JSON.stringify(data);
    }
    
    broadcastToContentScripts(message) {
        for (const [tabId, port] of this.contentScriptPorts) {
            try {
                port.postMessage(message);
            } catch (error) {
                console.error(`Failed to send message to tab ${tabId}:`, error);
                this.contentScriptPorts.delete(tabId);
            }
        }
    }
    
    async handleStartup() {
        console.log('Extension startup');
        await this.initializeStorage();
    }
    
    async handleInstall() {
        console.log('Extension installed');
        await this.initializeStorage();
    }
    
    async handleSuspend() {
        console.log('Extension suspending');
        await this.persistState();
    }
    
    async persistState() {
        await chrome.storage.local.set({
            extensionState: {
                persistentTimers: Array.from(this.persistentTimers.entries()),
                performanceMetrics: this.performanceMetrics,
                timestamp: Date.now()
            }
        });
    }
    
    async restoreState(state) {
        if (state.persistentTimers) {
            this.persistentTimers = new Map(state.persistentTimers);
        }
        if (state.performanceMetrics) {
            this.performanceMetrics = { ...this.performanceMetrics, ...state.performanceMetrics };
        }
    }
    
    reduceTimerFrequency(factor) {
        // Reduce frequency of non-critical timers
        for (const [id, timerInfo] of this.persistentTimers) {
            if (!timerInfo.options.critical) {
                timerInfo.intervalMinutes *= (1 / factor);
                chrome.alarms.create(id, {
                    periodInMinutes: timerInfo.intervalMinutes
                });
            }
        }
    }
}

// Initialize background manager
const backgroundManager = new ExtensionBackgroundManager();

// Export for testing
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ExtensionBackgroundManager;
}
