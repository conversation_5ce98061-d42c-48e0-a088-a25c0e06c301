/**
 * Dashboard Templates - Modular HTML templates for Chrome extension optimization
 * Extracted from massive dashboard.js template for better memory management
 */

class DashboardTemplates {
    constructor() {
        this.templateCache = window.ExtensionTemplateCache;
        this.templates = new Map();
        this.compressionEnabled = true;
        
        this.init();
    }
    
    async init() {
        try {
            await this.loadTemplatesFromCache();
            this.defineTemplateChunks();
            console.log('DashboardTemplates initialized successfully');
        } catch (error) {
            console.error('DashboardTemplates initialization failed:', error);
            // Fallback to just defining template chunks
            this.defineTemplateChunks();
        }
    }
    
    async loadTemplatesFromCache() {
        if (!this.templateCache) {
            console.warn('ExtensionTemplateCache not available, using memory-only templates');
            return;
        }
        
        // Try to load cached templates
        const cachedTemplates = await this.templateCache.getTemplate('dashboard-main');
        if (cachedTemplates) {
            this.templates = new Map(Object.entries(cachedTemplates));
            console.log('Loaded dashboard templates from cache');
        }
    }
    
    defineTemplateChunks() {
        // Header section template
        this.templates.set('header', `
            <div class="dashboard-header">
                <div class="dashboard-header-content">
                    <h1>Dashboard</h1>
                    <div class="database-container">
                        <div class="database-row">
                            <div class="database-left">
                                <!-- Time Tracker Components -->
                                <div class="time-tracker-container">
                                    <!-- Local Time Block -->
                                    <div class="time-tracker-block">
                                        <span class="time-tracker-label">Local Time</span>
                                        <div class="time-tracker-content">
                                            <img src="./assets/timezone-ic.svg" alt="Timezone Icon" class="time-tracker-icon" width="12" height="12" />
                                            <div class="time-tracker-time" id="local-time-display">12:00:00 AM</div>
                                        </div>
                                    </div>
                                    <!-- Divider between time trackers -->
                                    <div class="time-tracker-divider"></div>
                                    <!-- Pacific Time Block -->
                                    <div class="time-tracker-block">
                                        <span class="time-tracker-label">Pacific Time</span>
                                        <div class="time-tracker-content">
                                            <img src="./assets/timezone-ic.svg" alt="Timezone Icon" class="time-tracker-icon" width="12" height="12" />
                                            <div class="time-tracker-time" id="pacific-time-display">12:00:00 AM</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="database-right">
                                <div class="database-controls">
                                    <div class="privacy-mode-toggle-container">
                                        <!-- Privacy mode toggle will be inserted here -->
                                    </div>
                                    <div class="refresh-control-container">
                                        <!-- Refresh control will be inserted here -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `);
        
        // Sales cards section template
        this.templates.set('salesCards', `
            <div class="four-sales-cards-section">
                <div class="sales-cards-row">
                    <!-- Today's Sales Card -->
                    <div class="current-month-card-div">
                        <div class="sales-card-header">
                            <div class="title-date-section">
                                <img src="./assets/sales-ic.svg" alt="Sales Icon" class="sales-card-icon" width="16" height="16" />
                                <div class="title-date-text">
                                    <span class="sales-card-title">Today's Sales</span>
                                    <span class="sales-card-date">December 26, 2024</span>
                                </div>
                            </div>
                        </div>
                        <div class="sales-summary-div">
                            <span class="sales-count">0</span>
                        </div>
                        <hr class="sales-section-divider" />
                        <div class="marketplaces-div">
                            <div class="marketplaces-row">
                                <div class="marketplace-col all-marketplaces active" data-tooltip="All Marketplaces">
                                    <img src="./assets/all-marketplaces-active-ic.svg" alt="All Marketplaces Active" class="marketplace-icon" width="28" height="28" />
                                    <span class="marketplace-total-sales">0</span>
                                </div>
                                <div class="marketplace-col us inactive" data-tooltip="United States">
                                    <img src="./assets/US.svg" alt="US" class="marketplace-icon" width="28" height="28" />
                                    <span class="marketplace-total-sales">0</span>
                                </div>
                                <div class="marketplace-col uk inactive" data-tooltip="United Kingdom">
                                    <img src="./assets/UK.svg" alt="UK" class="marketplace-icon" width="28" height="28" />
                                    <span class="marketplace-total-sales">0</span>
                                </div>
                                <div class="marketplace-col de inactive" data-tooltip="Germany">
                                    <img src="./assets/DE.svg" alt="DE" class="marketplace-icon" width="28" height="28" />
                                    <span class="marketplace-total-sales">0</span>
                                </div>
                                <div class="marketplace-col fr inactive" data-tooltip="France">
                                    <img src="./assets/FR.svg" alt="FR" class="marketplace-icon" width="28" height="28" />
                                    <span class="marketplace-total-sales">0</span>
                                </div>
                                <div class="marketplace-col it inactive" data-tooltip="Italy">
                                    <img src="./assets/IT.svg" alt="IT" class="marketplace-icon" width="28" height="28" />
                                    <span class="marketplace-total-sales">0</span>
                                </div>
                                <div class="marketplace-col es inactive" data-tooltip="Spain">
                                    <img src="./assets/ES.svg" alt="ES" class="marketplace-icon" width="28" height="28" />
                                    <span class="marketplace-total-sales">0</span>
                                </div>
                                <div class="marketplace-col jp inactive" data-tooltip="Japan">
                                    <img src="./assets/JP.svg" alt="JP" class="marketplace-icon" width="28" height="28" />
                                    <span class="marketplace-total-sales">0</span>
                                </div>
                            </div>
                        </div>
                        <hr class="sales-section-divider" />
                        <div class="analytics-div">
                            <div class="metric-item">
                                <div class="metric-header">
                                    <span class="metric-label">Royalties</span>
                                </div>
                                <span class="metric-value royalties">$0.0</span>
                            </div>
                            <div class="metric-item">
                                <div class="metric-header">
                                    <span class="metric-label">Returned</span>
                                </div>
                                <span class="metric-value returned">0</span>
                                <span class="metric-percentage returned-percentage" style="display: none;">0.0%</span>
                            </div>
                            <div class="metric-item">
                                <div class="metric-header">
                                    <span class="metric-label">New</span>
                                </div>
                                <span class="metric-value new">0</span>
                                <span class="metric-percentage new-percentage" style="display: none;">0.0%</span>
                            </div>
                            <div class="metric-item">
                                <div class="metric-header">
                                    <span class="metric-label">Ads</span>
                                </div>
                                <span class="metric-value ads">0</span>
                                <span class="metric-percentage ads-percentage" style="display: none;">0.0%</span>
                            </div>
                        </div>
                        <div class="comparison-container" style="display: none;">
                            <div class="comparison-content">
                                <img src="./assets/up-per-ic.svg" alt="Up" class="comparison-arrow" width="12" height="12" />
                                <span class="comparison-percentage positive">+0.0%</span>
                            </div>
                            <span class="comparison-label">Compared to yesterday</span>
                        </div>
                    </div>
                    
                    <!-- Yesterday's Sales Card -->
                    <div class="last-month-card-div">
                        <div class="sales-card-header">
                            <div class="title-date-section">
                                <img src="./assets/sales-ic.svg" alt="Sales Icon" class="sales-card-icon" width="16" height="16" />
                                <div class="title-date-text">
                                    <span class="sales-card-title">Yesterday's Sales</span>
                                    <span class="sales-card-date">December 25, 2024</span>
                                </div>
                            </div>
                        </div>
                        <div class="sales-summary-div">
                            <span class="sales-count">0</span>
                        </div>
                        <hr class="sales-section-divider" />
                        <div class="marketplaces-div">
                            <div class="marketplaces-row">
                                <div class="marketplace-col all-marketplaces active" data-tooltip="All Marketplaces">
                                    <img src="./assets/all-marketplaces-active-ic.svg" alt="All Marketplaces Active" class="marketplace-icon" width="28" height="28" />
                                    <span class="marketplace-total-sales">0</span>
                                </div>
                                <div class="marketplace-col us inactive" data-tooltip="United States">
                                    <img src="./assets/US.svg" alt="US" class="marketplace-icon" width="28" height="28" />
                                    <span class="marketplace-total-sales">0</span>
                                </div>
                                <div class="marketplace-col uk inactive" data-tooltip="United Kingdom">
                                    <img src="./assets/UK.svg" alt="UK" class="marketplace-icon" width="28" height="28" />
                                    <span class="marketplace-total-sales">0</span>
                                </div>
                                <div class="marketplace-col de inactive" data-tooltip="Germany">
                                    <img src="./assets/DE.svg" alt="DE" class="marketplace-icon" width="28" height="28" />
                                    <span class="marketplace-total-sales">0</span>
                                </div>
                                <div class="marketplace-col fr inactive" data-tooltip="France">
                                    <img src="./assets/FR.svg" alt="FR" class="marketplace-icon" width="28" height="28" />
                                    <span class="marketplace-total-sales">0</span>
                                </div>
                                <div class="marketplace-col it inactive" data-tooltip="Italy">
                                    <img src="./assets/IT.svg" alt="IT" class="marketplace-icon" width="28" height="28" />
                                    <span class="marketplace-total-sales">0</span>
                                </div>
                                <div class="marketplace-col es inactive" data-tooltip="Spain">
                                    <img src="./assets/ES.svg" alt="ES" class="marketplace-icon" width="28" height="28" />
                                    <span class="marketplace-total-sales">0</span>
                                </div>
                                <div class="marketplace-col jp inactive" data-tooltip="Japan">
                                    <img src="./assets/JP.svg" alt="JP" class="marketplace-icon" width="28" height="28" />
                                    <span class="marketplace-total-sales">0</span>
                                </div>
                            </div>
                        </div>
                        <hr class="sales-section-divider" />
                        <div class="analytics-div">
                            <div class="metric-item">
                                <div class="metric-header">
                                    <span class="metric-label">Royalties</span>
                                </div>
                                <span class="metric-value royalties">$0.0</span>
                            </div>
                            <div class="metric-item">
                                <div class="metric-header">
                                    <span class="metric-label">Returned</span>
                                </div>
                                <span class="metric-value returned">0</span>
                                <span class="metric-percentage returned-percentage" style="display: none;">0.0%</span>
                            </div>
                            <div class="metric-item">
                                <div class="metric-header">
                                    <span class="metric-label">New</span>
                                </div>
                                <span class="metric-value new">0</span>
                                <span class="metric-percentage new-percentage" style="display: none;">0.0%</span>
                            </div>
                            <div class="metric-item">
                                <div class="metric-header">
                                    <span class="metric-label">Ads</span>
                                </div>
                                <span class="metric-value ads">0</span>
                                <span class="metric-percentage ads-percentage" style="display: none;">0.0%</span>
                            </div>
                        </div>
                        <div class="comparison-container" style="display: none;">
                            <div class="comparison-content">
                                <img src="./assets/up-per-ic.svg" alt="Up" class="comparison-arrow" width="12" height="12" />
                                <span class="comparison-percentage positive">+0.0%</span>
                            </div>
                            <span class="comparison-label">Compared to day before yesterday</span>
                        </div>
                    </div>
                    
                    <!-- Last Week Sales Card -->
                    <div class="current-year-card-div">
                        <div class="sales-card-header">
                            <div class="title-date-section">
                                <img src="./assets/sales-ic.svg" alt="Sales Icon" class="sales-card-icon" width="16" height="16" />
                                <div class="title-date-text">
                                    <span class="sales-card-title">Last Week Sales</span>
                                    <span class="sales-card-date">Dec 19 - Dec 25, 2024</span>
                                </div>
                            </div>
                        </div>
                        <div class="sales-summary-div">
                            <span class="sales-count">0</span>
                        </div>
                        <hr class="sales-section-divider" />
                        <div class="marketplaces-div">
                            <div class="marketplaces-row">
                                <div class="marketplace-col all-marketplaces active" data-tooltip="All Marketplaces">
                                    <img src="./assets/all-marketplaces-active-ic.svg" alt="All Marketplaces Active" class="marketplace-icon" width="28" height="28" />
                                    <span class="marketplace-total-sales">0</span>
                                </div>
                                <div class="marketplace-col us inactive" data-tooltip="United States">
                                    <img src="./assets/US.svg" alt="US" class="marketplace-icon" width="28" height="28" />
                                    <span class="marketplace-total-sales">0</span>
                                </div>
                                <div class="marketplace-col uk inactive" data-tooltip="United Kingdom">
                                    <img src="./assets/UK.svg" alt="UK" class="marketplace-icon" width="28" height="28" />
                                    <span class="marketplace-total-sales">0</span>
                                </div>
                                <div class="marketplace-col de inactive" data-tooltip="Germany">
                                    <img src="./assets/DE.svg" alt="DE" class="marketplace-icon" width="28" height="28" />
                                    <span class="marketplace-total-sales">0</span>
                                </div>
                                <div class="marketplace-col fr inactive" data-tooltip="France">
                                    <img src="./assets/FR.svg" alt="FR" class="marketplace-icon" width="28" height="28" />
                                    <span class="marketplace-total-sales">0</span>
                                </div>
                                <div class="marketplace-col it inactive" data-tooltip="Italy">
                                    <img src="./assets/IT.svg" alt="IT" class="marketplace-icon" width="28" height="28" />
                                    <span class="marketplace-total-sales">0</span>
                                </div>
                                <div class="marketplace-col es inactive" data-tooltip="Spain">
                                    <img src="./assets/ES.svg" alt="ES" class="marketplace-icon" width="28" height="28" />
                                    <span class="marketplace-total-sales">0</span>
                                </div>
                                <div class="marketplace-col jp inactive" data-tooltip="Japan">
                                    <img src="./assets/JP.svg" alt="JP" class="marketplace-icon" width="28" height="28" />
                                    <span class="marketplace-total-sales">0</span>
                                </div>
                            </div>
                        </div>
                        <hr class="sales-section-divider" />
                        <div class="analytics-div">
                            <div class="metric-item">
                                <div class="metric-header">
                                    <span class="metric-label">Royalties</span>
                                </div>
                                <span class="metric-value royalties">$0.0</span>
                            </div>
                            <div class="metric-item">
                                <div class="metric-header">
                                    <span class="metric-label">Returned</span>
                                </div>
                                <span class="metric-value returned">0</span>
                                <span class="metric-percentage returned-percentage" style="display: none;">0.0%</span>
                            </div>
                            <div class="metric-item">
                                <div class="metric-header">
                                    <span class="metric-label">New</span>
                                </div>
                                <span class="metric-value new">0</span>
                                <span class="metric-percentage new-percentage" style="display: none;">0.0%</span>
                            </div>
                            <div class="metric-item">
                                <div class="metric-header">
                                    <span class="metric-label">Ads</span>
                                </div>
                                <span class="metric-value ads">0</span>
                                <span class="metric-percentage ads-percentage" style="display: none;">0.0%</span>
                            </div>
                        </div>
                        <div class="comparison-container" style="display: none;">
                            <div class="comparison-content">
                                <img src="./assets/up-per-ic.svg" alt="Up" class="comparison-arrow" width="12" height="12" />
                                <span class="comparison-percentage positive">+0.0%</span>
                            </div>
                            <span class="comparison-label">Compared to week before last</span>
                        </div>
                    </div>
                </div>
            </div>
        `);
        
        // Cache templates for future use
        this.cacheTemplates();
    }
    
    async cacheTemplates() {
        if (!this.templateCache) return;
        
        try {
            const templatesObject = Object.fromEntries(this.templates);
            await this.templateCache.setTemplate('dashboard-main', templatesObject);
            console.log('Dashboard templates cached successfully');
        } catch (error) {
            console.error('Failed to cache dashboard templates:', error);
        }
    }
    
    getTemplate(templateName) {
        return this.templates.get(templateName) || '';
    }
    
    async getTemplateAsync(templateName) {
        // Try memory cache first
        if (this.templates.has(templateName)) {
            return this.templates.get(templateName);
        }
        
        // Try extension cache
        if (this.templateCache) {
            const cached = await this.templateCache.getTemplate(`dashboard-${templateName}`);
            if (cached) {
                this.templates.set(templateName, cached);
                return cached;
            }
        }
        
        return '';
    }
    
    assembleMainTemplate() {
        const header = this.getTemplate('header');
        const salesCards = this.getTemplate('salesCards');

        // If templates are not available, return a basic fallback
        if (!header && !salesCards) {
            return this.getFallbackTemplate();
        }

        return `
            <div class="dashboard-component">
                ${header}
                ${salesCards}
                <!-- Additional sections will be loaded dynamically -->
            </div>
        `;
    }

    getFallbackTemplate() {
        return `
            <div class="dashboard-component">
                <div class="dashboard-header">
                    <h1>Dashboard</h1>
                    <p>Loading dashboard components...</p>
                </div>
                <div class="dashboard-content">
                    <div class="loading-message">
                        <p>Dashboard is initializing. Please wait...</p>
                    </div>
                </div>
            </div>
        `;
    }
    
    async assembleMainTemplateAsync() {
        try {
            const header = await this.getTemplateAsync('header');
            const salesCards = await this.getTemplateAsync('salesCards');

            // If templates are not available, return a basic fallback
            if (!header && !salesCards) {
                console.warn('Templates not available, using fallback');
                return this.getFallbackTemplate();
            }

            return `
                <div class="dashboard-component">
                    ${header}
                    ${salesCards}
                    <!-- Additional sections will be loaded dynamically -->
                </div>
            `;
        } catch (error) {
            console.error('Failed to assemble template:', error);
            return this.getFallbackTemplate();
        }
    }
    
    clearCache() {
        this.templates.clear();
        if (this.templateCache) {
            this.templateCache.clearCache();
        }
    }
    
    getStats() {
        return {
            templatesLoaded: this.templates.size,
            cacheEnabled: !!this.templateCache,
            compressionEnabled: this.compressionEnabled
        };
    }
}

// Create global instance
window.DashboardTemplates = window.DashboardTemplates || new DashboardTemplates();

// Export for modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DashboardTemplates;
}
