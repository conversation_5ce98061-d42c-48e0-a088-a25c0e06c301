// Simple security helper functions (inline to avoid import issues)
function safeSetHTML(element, html, options = {}) {
  if (!element) return false;
  if (options.trusted === true) {
    element.innerHTML = html;
  } else {
    // Basic sanitization - remove script tags and dangerous attributes
    const sanitized = html
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/\s*on\w+\s*=\s*["'][^"']*["']/gi, '')
      .replace(/href\s*=\s*["']javascript:[^"']*["']/gi, '');
    element.innerHTML = sanitized;
  }
  return true;
}

function sanitizeUserInput(input) {
  if (typeof input !== 'string') return '';
  return input
    .replace(/javascript:/gi, '')
    .replace(/on\w+=/gi, '');
}

function sanitizeUserInputStrict(input) {
  if (typeof input !== 'string') return '';
  return input
    .replace(/[<>]/g, '')
    .replace(/javascript:/gi, '')
    .replace(/on\w+=/gi, '');
}

function updateTextContent(element, text) {
  if (!element) return false;
  element.textContent = text || '';
  return true;
}

function validateSearchInput(input) {
  if (typeof input !== 'string') return { isValid: false, errors: ['Input must be string'] };
  if (input.length > 500) return { isValid: false, errors: ['Input too long'] };
  if (/<script|javascript:|on\w+=/i.test(input)) return { isValid: false, errors: ['Invalid characters'] };
  return { isValid: true, value: input.trim() };
}

/**
 * Ensure initial zero styling is correct for Four Sales and Top Four Sales sections.
 * Idempotent and safe to run multiple times.
 */
function applyInitialZeroClassForSalesSections() {
  try {
    const selectors = [
      '.four-sales-cards-section .current-month-card-div .sales-count',
      '.four-sales-cards-section .last-month-card-div .sales-count',
      '.four-sales-cards-section .current-year-card-div .sales-count',
      '.four-sales-cards-section .last-year-card-div .sales-count',
      '.top-four-sales-cards-section .top-day-card-div .sales-count',
      '.top-four-sales-cards-section .top-week-card-div .sales-count',
      '.top-four-sales-cards-section .top-month-card-div .sales-count',
      '.top-four-sales-cards-section .top-year-card-div .sales-count'
    ];
    document.querySelectorAll(selectors.join(',')).forEach((el) => {
      const numeric = parseInt((el.textContent || '0').replace(/,/g, ''), 10) || 0;
      el.classList.remove('zero', 'has-value', 'positive', 'negative');
      el.classList.add(numeric === 0 ? 'zero' : 'has-value');
    });
  } catch (err) {
    if (window.SnapLogger) {
      window.SnapLogger.warn('applyInitialZeroClassForSalesSections failed:', err);
    } else {
      console.warn('applyInitialZeroClassForSalesSections failed:', err);
    }
  }
}

/**
 * Extension-optimized dashboard initialization
 * Uses ExtensionTemplateCache and modular loading for better memory management
 */
async function initializeDashboard() {
  try {
    // Initialize extension utilities
    const timerManager = window.ExtensionTimerManager;
    const domOptimizer = window.ExtensionDOMOptimizer;
    const templateCache = window.DashboardTemplates;
    
    if (!templateCache) {
      console.error('DashboardTemplates not available');
      return false;
    }
    
    // Get main template asynchronously
    const dashboardHTML = await templateCache.assembleMainTemplateAsync();
    
    if (!dashboardHTML) {
      console.error('Failed to load dashboard template');
      return false;
    }
    
    // Use extension DOM optimizer for efficient rendering
    const dashboardContainer = document.querySelector('.dashboard-container') || document.body;
    await domOptimizer.updateElement(dashboardContainer, {
      innerHTML: dashboardHTML
    });
    
    // Initialize dashboard components with extension-aware timers
    initializeDashboardComponents();
    
    return true;
  } catch (error) {
    console.error('Dashboard initialization failed:', error);
    return false;
  }
}

/**
 * Initialize dashboard components with extension lifecycle management
 */
function initializeDashboardComponents() {
  const timerManager = window.ExtensionTimerManager;
  
  // Initialize time trackers with extension-aware timers
  if (timerManager) {
    // Local time update timer
    timerManager.setInterval(() => {
      updateLocalTime();
    }, 1000, timerManager.timerCategories.UI, { persistent: false });
    
    // Pacific time update timer
    timerManager.setInterval(() => {
      updatePacificTime();
    }, 1000, timerManager.timerCategories.UI, { persistent: false });
    
    // Database status update timer
    timerManager.setInterval(() => {
      updateDatabaseStatus();
    }, 30000, timerManager.timerCategories.BACKGROUND, { persistent: true });
  }
  
  // Initialize privacy mode toggle
  initializePrivacyModeToggle();
  
  // Initialize refresh control
  initializeRefreshControl();
  
  // Initialize sales cards
  initializeSalesCards();
  
  // Apply initial zero styling
  applyInitialZeroClassForSalesSections();
}

/**
 * Update local time display
 */
function updateLocalTime() {
  const localTimeDisplay = document.getElementById('local-time-display');
  if (localTimeDisplay) {
    const now = new Date();
    localTimeDisplay.textContent = now.toLocaleTimeString();
  }
}

/**
 * Update Pacific time display
 */
function updatePacificTime() {
  const pacificTimeDisplay = document.getElementById('pacific-time-display');
  if (pacificTimeDisplay && window.SnapTimezone) {
    const pacificTime = window.SnapTimezone.getPacificTime();
    pacificTimeDisplay.textContent = pacificTime.toLocaleTimeString();
  }
}

/**
 * Update database status
 */
function updateDatabaseStatus() {
  // Implementation for database status updates
  console.log('Database status updated');
}

/**
 * Initialize privacy mode toggle
 */
function initializePrivacyModeToggle() {
  const container = document.querySelector('.privacy-mode-toggle-container');
  if (container && window.getDashboardPrivacyModeHTML) {
    container.innerHTML = window.getDashboardPrivacyModeHTML();
  }
}

/**
 * Initialize refresh control
 */
function initializeRefreshControl() {
  const container = document.querySelector('.refresh-control-container');
  if (container && window.getDashboardRefreshHTML) {
    container.innerHTML = window.getDashboardRefreshHTML();
  }
}

/**
 * Initialize sales cards
 */
function initializeSalesCards() {
  // Implementation for sales cards initialization
  console.log('Sales cards initialized');
}

// Legacy dashboard HTML variable for backward compatibility
const dashboardHTML = '';

// Initialize dashboard when DOM is ready
if (typeof document !== 'undefined') {
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeDashboard);
  } else {
    initializeDashboard();
  }
}

/**
 * Format number with comma separators
 * @param {number} num - The number to format
 * @returns {string} - Formatted number with commas
 */
function formatNumberWithCommas(num) {
  if (typeof num !== 'number' || isNaN(num)) return '0';
  return num.toLocaleString();
}

/**
 * Format returned units for display (with parentheses)
 * @param {number} returnedValue - The returned value
 * @returns {string} - Formatted returned units
 */
function formatReturnedUnits(returnedValue) {
  if (returnedValue === 0) {
    return '(0)';
  } else if (returnedValue > 0) {
    return `(-${formatNumberWithCommas(returnedValue)})`;
  } else {
    // Already negative, make positive for display
    return `(-${formatNumberWithCommas(Math.abs(returnedValue))})`;
  }
}

/**
 * Format returned units for metric display (without parentheses)
 * @param {number} returnedValue - The returned value
 * @returns {string} - Formatted returned metric
 */
function formatReturnedMetric(returnedValue) {
  if (returnedValue === 0) {
    return '0';
  } else {
    return `(-${formatNumberWithCommas(Math.abs(returnedValue))})`;
  }
}

// Export functions for global access
window.initializeDashboard = initializeDashboard;
window.initializeDashboardComponents = initializeDashboardComponents;
window.applyInitialZeroClassForSalesSections = applyInitialZeroClassForSalesSections;
window.formatNumberWithCommas = formatNumberWithCommas;
window.formatReturnedUnits = formatReturnedUnits;
window.formatReturnedMetric = formatReturnedMetric;
